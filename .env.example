# 資料庫配置
DATABASE_URL="postgresql://postgres:password123@localhost:5432/breakfast_pos_dev"
POSTGRES_PASSWORD="your-secure-password"

# Redis 配置
REDIS_URL="redis://localhost:6379"

# JWT 配置
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="7d"

# API 配置
PORT=3001
NODE_ENV="development"

# 前端配置
VITE_API_URL="http://localhost:3001"
VITE_SOCKET_URL="http://localhost:3001"

# 支付配置
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# 打印機配置
PRINTER_RECEIPT_IP="*************"
PRINTER_KITCHEN_IP="*************"

# 郵件配置 (可選)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# 檔案上傳配置
MAX_FILE_SIZE="5MB"
UPLOAD_PATH="./uploads"

# 安全配置
CORS_ORIGIN="http://localhost:3000,http://localhost:3002,http://localhost:3003"
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 監控配置
LOG_LEVEL="info"
LOG_FILE_PATH="./logs"

# 備份配置
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
