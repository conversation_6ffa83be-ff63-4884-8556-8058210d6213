{"name": "breakfast-pos-kds-system", "version": "1.0.0", "description": "早餐店 POS 與 KDS 系統", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:pos\" \"npm run dev:kds\" \"npm run dev:admin\"", "dev:backend": "cd backend && npm run dev", "dev:pos": "cd frontend/pos && npm run dev", "dev:kds": "cd frontend/kds && npm run dev", "dev:admin": "cd frontend/admin && npm run dev", "build": "npm run build:backend && npm run build:pos && npm run build:kds && npm run build:admin", "build:backend": "cd backend && npm run build", "build:pos": "cd frontend/pos && npm run build", "build:kds": "cd frontend/kds && npm run build", "build:admin": "cd frontend/admin && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend/pos && npm test && cd ../kds && npm test && cd ../admin && npm test", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "db:migrate": "cd backend && npx prisma migrate dev", "db:seed": "cd backend && npx prisma db seed", "db:studio": "cd backend && npx prisma studio"}, "keywords": ["pos", "kds", "restaurant", "breakfast", "point-of-sale", "kitchen-display"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend/pos", "frontend/kds", "frontend/admin"]}