version: '3.8'

services:
  # PostgreSQL 資料庫
  postgres:
    image: postgres:15
    container_name: breakfast-pos-db-dev
    environment:
      POSTGRES_DB: breakfast_pos_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - breakfast-pos-network

  # Redis (用於 Session 和快取)
  redis:
    image: redis:7-alpine
    container_name: breakfast-pos-redis-dev
    ports:
      - "6379:6379"
    networks:
      - breakfast-pos-network

  # 後端 API 服務
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: breakfast-pos-backend-dev
    environment:
      NODE_ENV: development
      DATABASE_URL: ***********************************************/breakfast_pos_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-jwt-secret-key-dev
      PORT: 3001
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - breakfast-pos-network
    command: npm run dev

  # POS 前端
  pos-frontend:
    build:
      context: ./frontend/pos
      dockerfile: Dockerfile.dev
    container_name: breakfast-pos-frontend-dev
    environment:
      VITE_API_URL: http://localhost:3001
      VITE_SOCKET_URL: http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/pos:/app
      - /app/node_modules
    networks:
      - breakfast-pos-network
    command: npm run dev

  # KDS 前端
  kds-frontend:
    build:
      context: ./frontend/kds
      dockerfile: Dockerfile.dev
    container_name: breakfast-kds-frontend-dev
    environment:
      VITE_API_URL: http://localhost:3001
      VITE_SOCKET_URL: http://localhost:3001
    ports:
      - "3002:3002"
    volumes:
      - ./frontend/kds:/app
      - /app/node_modules
    networks:
      - breakfast-pos-network
    command: npm run dev

  # 管理後台前端
  admin-frontend:
    build:
      context: ./frontend/admin
      dockerfile: Dockerfile.dev
    container_name: breakfast-admin-frontend-dev
    environment:
      VITE_API_URL: http://localhost:3001
      VITE_SOCKET_URL: http://localhost:3001
    ports:
      - "3003:3003"
    volumes:
      - ./frontend/admin:/app
      - /app/node_modules
    networks:
      - breakfast-pos-network
    command: npm run dev

volumes:
  postgres_data_dev:

networks:
  breakfast-pos-network:
    driver: bridge
