version: '3.8'

services:
  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: breakfast-pos-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/pos/dist:/usr/share/nginx/html/pos
      - ./frontend/kds/dist:/usr/share/nginx/html/kds
      - ./frontend/admin/dist:/usr/share/nginx/html/admin
    depends_on:
      - backend
    networks:
      - breakfast-pos-network
    restart: unless-stopped

  # PostgreSQL 資料庫
  postgres:
    image: postgres:15
    container_name: breakfast-pos-db-prod
    environment:
      POSTGRES_DB: breakfast_pos_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./backup:/backup
    networks:
      - breakfast-pos-network
    restart: unless-stopped

  # Redis
  redis:
    image: redis:7-alpine
    container_name: breakfast-pos-redis-prod
    volumes:
      - redis_data_prod:/data
    networks:
      - breakfast-pos-network
    restart: unless-stopped

  # 後端 API 服務
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: breakfast-pos-backend-prod
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/breakfast_pos_prod
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 3001
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - breakfast-pos-network
    restart: unless-stopped

volumes:
  postgres_data_prod:
  redis_data_prod:

networks:
  breakfast-pos-network:
    driver: bridge
