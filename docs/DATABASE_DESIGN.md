# 資料庫設計文檔

## 概述

本系統使用 PostgreSQL 作為主要資料庫，通過 Prisma ORM 進行資料庫操作。設計支援多門店、多用戶角色、完整的訂單流程管理。

## 核心資料表

### 1. 用戶表 (users)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| email | String | 電子郵件 | UNIQUE, NOT NULL |
| username | String | 用戶名 | UNIQUE, NOT NULL |
| password | String | 密碼 (加密) | NOT NULL |
| name | String | 真實姓名 | NOT NULL |
| role | UserRole | 用戶角色 | ENUM |
| isActive | Boolean | 是否啟用 | DEFAULT true |
| storeId | String | 所屬門店 | FK |

**角色類型 (UserRole):**
- ADMIN: 系統管理員
- MANAGER: 店長
- CASHIER: 收銀員
- KITCHEN: 廚房人員

### 2. 門店表 (stores)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| name | String | 門店名稱 | NOT NULL |
| address | String | 地址 | |
| phone | String | 電話 | |
| email | String | 電子郵件 | |
| isActive | Boolean | 是否營業 | DEFAULT true |

### 3. 菜品分類表 (categories)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| name | String | 分類名稱 | NOT NULL |
| description | String | 分類描述 | |
| sortOrder | Int | 排序順序 | DEFAULT 0 |
| isActive | Boolean | 是否啟用 | DEFAULT true |
| storeId | String | 所屬門店 | FK |

### 4. 菜品表 (menu_items)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| name | String | 菜品名稱 | NOT NULL |
| description | String | 菜品描述 | |
| price | Decimal | 售價 | NOT NULL |
| cost | Decimal | 成本 | |
| image | String | 圖片路徑 | |
| isActive | Boolean | 是否啟用 | DEFAULT true |
| isAvailable | Boolean | 是否可售 | DEFAULT true |
| categoryId | String | 分類ID | FK |
| storeId | String | 門店ID | FK |
| stock | Int | 庫存數量 | DEFAULT 0 |
| lowStockAlert | Int | 低庫存警告 | DEFAULT 5 |
| prepTime | Int | 製作時間(分鐘) | DEFAULT 5 |

### 5. 訂單表 (orders)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| orderNumber | String | 訂單編號 | UNIQUE |
| status | OrderStatus | 訂單狀態 | ENUM |
| customerName | String | 客戶姓名 | |
| customerPhone | String | 客戶電話 | |
| subtotal | Decimal | 小計 | NOT NULL |
| tax | Decimal | 稅額 | DEFAULT 0 |
| discount | Decimal | 折扣 | DEFAULT 0 |
| total | Decimal | 總計 | NOT NULL |
| paymentMethod | PaymentMethod | 支付方式 | ENUM |
| paymentStatus | PaymentStatus | 支付狀態 | ENUM |
| paidAt | DateTime | 支付時間 | |
| orderType | OrderType | 訂單類型 | ENUM |
| notes | String | 備註 | |
| orderTime | DateTime | 下單時間 | DEFAULT now() |
| estimatedTime | DateTime | 預計完成時間 | |
| completedAt | DateTime | 完成時間 | |
| storeId | String | 門店ID | FK |
| userId | String | 操作員ID | FK |

**訂單狀態 (OrderStatus):**
- PENDING: 待處理
- CONFIRMED: 已確認
- PREPARING: 製作中
- READY: 已完成
- DELIVERED: 已送達
- CANCELLED: 已取消

**支付方式 (PaymentMethod):**
- CASH: 現金
- CREDIT_CARD: 信用卡
- MOBILE_PAYMENT: 行動支付
- BANK_TRANSFER: 銀行轉帳

**支付狀態 (PaymentStatus):**
- PENDING: 待支付
- PAID: 已支付
- FAILED: 支付失敗
- REFUNDED: 已退款

**訂單類型 (OrderType):**
- DINE_IN: 內用
- TAKEAWAY: 外帶
- DELIVERY: 外送

### 6. 訂單項目表 (order_items)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| quantity | Int | 數量 | NOT NULL |
| unitPrice | Decimal | 單價 | NOT NULL |
| totalPrice | Decimal | 小計 | NOT NULL |
| notes | String | 備註 | |
| orderId | String | 訂單ID | FK |
| menuItemId | String | 菜品ID | FK |

### 7. 庫存記錄表 (inventory_logs)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| type | InventoryType | 操作類型 | ENUM |
| quantity | Int | 數量 | NOT NULL |
| reason | String | 原因 | |
| menuItemId | String | 菜品ID | FK |
| userId | String | 操作員ID | FK |

**庫存操作類型 (InventoryType):**
- IN: 入庫
- OUT: 出庫
- ADJUST: 調整

### 8. 系統設定表 (settings)

| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| id | String | 主鍵 | PK, CUID |
| key | String | 設定鍵 | UNIQUE |
| value | String | 設定值 | NOT NULL |

## 關聯關係

### 一對多關係
- Store → Users (一個門店有多個用戶)
- Store → Categories (一個門店有多個分類)
- Store → MenuItems (一個門店有多個菜品)
- Store → Orders (一個門店有多個訂單)
- Category → MenuItems (一個分類有多個菜品)
- User → Orders (一個用戶可以處理多個訂單)
- Order → OrderItems (一個訂單有多個項目)
- MenuItem → OrderItems (一個菜品可以在多個訂單中)

## 索引設計

### 主要索引
- users.email (唯一索引)
- users.username (唯一索引)
- orders.orderNumber (唯一索引)
- settings.key (唯一索引)

### 複合索引
- orders(storeId, status, orderTime)
- order_items(orderId, menuItemId)
- menu_items(storeId, categoryId, isActive)

## 資料完整性

### 外鍵約束
- 所有關聯表都設置適當的外鍵約束
- 訂單項目在訂單刪除時級聯刪除

### 檢查約束
- 價格和金額必須為正數
- 庫存數量不能為負數
- 數量必須大於0

## 性能優化

### 查詢優化
- 為常用查詢欄位建立索引
- 使用複合索引優化多條件查詢
- 適當使用資料庫視圖

### 資料分割
- 考慮按門店分割大型表
- 歷史訂單資料歸檔策略

## 備份策略

### 定期備份
- 每日全量備份
- 每小時增量備份
- 重要操作前手動備份

### 災難恢復
- 異地備份存儲
- 快速恢復程序
- 資料一致性檢查
