# 早餐店 KDS 廚房顯示系統

## 功能特色

- 🍳 專為廚房設計的大屏顯示界面
- ⏰ 即時訂單計時和超時警告
- 🔄 訂單狀態即時同步
- 🎵 聲音提醒（新訂單、超時警告）
- 🏭 多工作站支援和訂單分配
- 📊 訂單統計和進度追蹤
- 🎨 直觀的顏色編碼和視覺提示
- 📱 響應式設計，支援各種螢幕尺寸

## 系統概述

KDS（Kitchen Display System）廚房顯示系統是專為餐廳廚房設計的訂單管理和顯示系統。它與 POS 系統無縫整合，為廚房人員提供清晰、即時的訂單信息。

## 主要功能

### 1. 訂單顯示
- **卡片式佈局**：每個訂單以卡片形式顯示，包含完整訂單信息
- **顏色編碼**：不同狀態的訂單使用不同顏色區分
  - 🟡 黃色：待處理訂單
  - 🔵 藍色：製作中訂單
  - 🟢 綠色：已完成訂單
  - 🔴 紅色：超時訂單
- **優先級顯示**：根據等待時間自動調整訂單優先級

### 2. 時間管理
- **即時計時**：顯示訂單已經過時間和預計剩餘時間
- **超時警告**：超過預計時間的訂單會高亮顯示並播放警告音
- **製作時間預估**：根據菜品類型自動計算預計製作時間

### 3. 訂單操作
- **開始製作**：點擊開始按鈕將訂單狀態更改為製作中
- **完成訂單**：製作完成後標記訂單為已完成
- **取消訂單**：支援取消訂單並記錄取消原因

### 4. 工作站管理
- **多工作站支援**：可將廚房分為不同工作站（如熱食區、飲品區）
- **訂單分配**：根據菜品類型自動或手動分配訂單到相應工作站
- **工作負載平衡**：顯示各工作站當前處理的訂單數量

### 5. 聲音提醒
- **新訂單提醒**：收到新訂單時播放提示音
- **超時警告**：訂單超時時播放警告音
- **音量控制**：可調整提示音音量或完全靜音

## 技術架構

### 前端技術
- **React 18** + **TypeScript** - 現代化前端框架
- **Ant Design** - 企業級 UI 組件庫
- **Zustand** - 輕量級狀態管理
- **Socket.IO Client** - 即時通訊
- **Vite** - 快速構建工具

### 即時通訊
- 與 POS 系統即時同步
- 訂單狀態變更即時推送
- 斷線重連機制
- 多設備同步支援

## 快速開始

### 環境要求
- Node.js 18+
- 現代瀏覽器（Chrome、Firefox、Safari、Edge）

### 安裝和運行
```bash
# 安裝依賴
npm install

# 配置環境變數
cp .env.example .env

# 開發模式運行
npm run dev
```

應用將在 http://localhost:3002 啟動

### 生產構建
```bash
npm run build
```

## 使用指南

### 登入系統
1. 使用廚房人員或管理員帳號登入
2. 系統會自動連接到對應門店的訂單流

### 處理訂單
1. **查看新訂單**：新訂單會自動出現在螢幕上，並播放提示音
2. **開始製作**：點擊「開始」按鈕開始處理訂單
3. **完成訂單**：製作完成後點擊「完成」按鈕
4. **取消訂單**：如需取消，點擊「取消」按鈕並填寫原因

### 訂單篩選
- 使用頂部的狀態篩選器選擇要顯示的訂單類型
- 支援多選和全選/清除操作

### 工作站模式
1. 在設定中啟用工作站功能
2. 配置不同的工作站（如熱食區、飲品區）
3. 訂單會根據菜品類型自動分配到相應工作站

## 系統設定

### 時間設定
- **預設製作時間**：當菜品沒有設定製作時間時的預設值
- **警告時間**：剩餘時間少於此值時顯示警告
- **超時時間**：超過預計時間多久後標記為超時

### 顯示設定
- **每頁訂單數**：同時顯示的最大訂單數量
- **自動刷新間隔**：系統更新頻率
- **已完成訂單顯示時間**：完成的訂單保留時間

### 聲音設定
- **啟用聲音**：開啟/關閉所有聲音提醒
- **新訂單聲音**：新訂單到達時的提示音
- **超時聲音**：訂單超時時的警告音
- **音量控制**：調整提示音音量

## 演示帳號

- **管理員**: <EMAIL> / admin123
- **廚房人員**: <EMAIL> / kitchen123

## 最佳實踐

### 硬體建議
- **螢幕尺寸**：建議使用 24 吋以上螢幕
- **解析度**：1920x1080 或更高
- **觸控支援**：支援觸控螢幕操作
- **音響設備**：配備音響以確保聲音提醒效果

### 使用建議
1. **定期檢查設定**：根據實際情況調整時間參數
2. **培訓員工**：確保廚房人員熟悉系統操作
3. **備用方案**：準備紙質訂單作為系統故障時的備用方案
4. **定期清潔**：保持螢幕清潔以確保良好的視覺效果

### 故障排除
- **連接問題**：檢查網路連接和後端服務狀態
- **聲音問題**：檢查瀏覽器音頻權限和系統音量
- **顯示問題**：嘗試刷新頁面或重新登入
- **同步問題**：檢查與 POS 系統的連接狀態

## 開發指南

### 項目結構
```
src/
├── components/          # 可重用組件
│   ├── kds/            # KDS 專用組件
│   ├── layout/         # 佈局組件
│   └── auth/           # 認證組件
├── pages/              # 頁面組件
├── stores/             # 狀態管理
├── services/           # API 和 Socket 服務
├── types/              # TypeScript 類型
├── utils/              # 工具函數
└── styles/             # 樣式文件
```

### 自定義開發
- **添加新的訂單狀態**：修改 `types/index.ts` 中的 `OrderStatus` 枚舉
- **自定義聲音**：替換 `public/sounds/` 目錄中的音頻文件
- **修改顏色主題**：編輯 `styles/index.css` 中的 CSS 變數
- **添加新的工作站類型**：擴展 `KitchenStation` 接口

## 部署

### Docker 部署
```bash
# 開發環境
docker build -f Dockerfile.dev -t kds-frontend:dev .
docker run -p 3002:3002 kds-frontend:dev

# 生產環境
docker build -f Dockerfile.prod -t kds-frontend:prod .
docker run -p 80:80 kds-frontend:prod
```

### 靜態部署
```bash
npm run build
# 將 dist/ 目錄部署到 Web 服務器
```

## 支援與反饋

如有問題或建議，請聯繫開發團隊或提交 Issue。
