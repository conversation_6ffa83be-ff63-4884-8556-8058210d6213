import { <PERSON>, Button, Badge } from 'antd'
import { 
  ClockCircleOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons'
import { OrderStatus } from '@/types'
import { useKDSStore } from '@/stores/kdsStore'

interface OrderFiltersProps {
  selectedStatus: OrderStatus[]
  onStatusChange: (status: OrderStatus[]) => void
}

const OrderFilters = ({ selectedStatus, onStatusChange }: OrderFiltersProps) => {
  const { stats } = useKDSStore()

  const statusFilters = [
    {
      key: OrderStatus.CONFIRMED,
      label: '待處理',
      icon: <ClockCircleOutlined />,
      count: stats.pending,
      color: '#faad14',
    },
    {
      key: OrderStatus.PREPARING,
      label: '製作中',
      icon: <LoadingOutlined />,
      count: stats.preparing,
      color: '#1890ff',
    },
    {
      key: OrderStatus.READY,
      label: '已完成',
      icon: <CheckCircleOutlined />,
      count: stats.ready,
      color: '#52c41a',
    },
  ]

  const toggleStatus = (status: OrderStatus) => {
    if (selectedStatus.includes(status)) {
      onStatusChange(selectedStatus.filter(s => s !== status))
    } else {
      onStatusChange([...selectedStatus, status])
    }
  }

  const selectAll = () => {
    onStatusChange([OrderStatus.CONFIRMED, OrderStatus.PREPARING, OrderStatus.READY])
  }

  const clearAll = () => {
    onStatusChange([])
  }

  return (
    <div style={{ 
      padding: '16px 0', 
      borderBottom: '1px solid #f0f0f0',
      background: 'white',
      marginBottom: 16,
    }}>
      <Space size="middle" wrap>
        <span style={{ fontWeight: 500, color: '#595959' }}>狀態篩選：</span>
        
        {statusFilters.map(filter => (
          <Button
            key={filter.key}
            type={selectedStatus.includes(filter.key) ? 'primary' : 'default'}
            icon={filter.icon}
            onClick={() => toggleStatus(filter.key)}
            style={{
              borderColor: selectedStatus.includes(filter.key) ? filter.color : undefined,
              backgroundColor: selectedStatus.includes(filter.key) ? filter.color : undefined,
            }}
          >
            <Badge 
              count={filter.count} 
              size="small" 
              style={{ 
                backgroundColor: selectedStatus.includes(filter.key) ? 'rgba(255,255,255,0.3)' : filter.color 
              }}
            >
              {filter.label}
            </Badge>
          </Button>
        ))}

        <div style={{ borderLeft: '1px solid #d9d9d9', paddingLeft: 16, marginLeft: 8 }}>
          <Space>
            <Button size="small" onClick={selectAll}>
              全選
            </Button>
            <Button size="small" onClick={clearAll}>
              清除
            </Button>
          </Space>
        </div>

        {/* 超時警告 */}
        {stats.overdueCount > 0 && (
          <div style={{ 
            marginLeft: 'auto',
            padding: '4px 12px',
            background: '#fff2f0',
            border: '1px solid #ffccc7',
            borderRadius: 6,
            color: '#ff4d4f',
          }}>
            <ExclamationCircleOutlined style={{ marginRight: 4 }} />
            {stats.overdueCount} 個訂單超時
          </div>
        )}
      </Space>
    </div>
  )
}

export default OrderFilters
