import { useState } from 'react'
import { <PERSON>, Typo<PERSON>, Space, Button, Modal, Input, Badge } from 'antd'
import { 
  ClockCircleOutlined, 
  PlayCircleOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PhoneOutlined,
  UserOutlined,
  ShopOutlined,
  CarOutlined,
  HomeOutlined,
} from '@ant-design/icons'
import { KDSOrder, OrderStatus, OrderType } from '@/types'
import { useKDSStore } from '@/stores/kdsStore'
import { socketService } from '@/services/socketService'
import dayjs from 'dayjs'

const { Text, Title } = Typography
const { TextArea } = Input

interface OrderCardProps {
  order: KDSOrder
}

const OrderCard = ({ order }: OrderCardProps) => {
  const { startOrder, completeOrder, cancelOrder } = useKDSStore()
  const [cancelModalVisible, setCancelModalVisible] = useState(false)
  const [cancelReason, setCancelReason] = useState('')

  const getStatusClass = () => {
    if (order.isOverdue) return 'overdue'
    
    switch (order.status) {
      case OrderStatus.PENDING:
      case OrderStatus.CONFIRMED:
        return 'pending'
      case OrderStatus.PREPARING:
        return 'preparing'
      case OrderStatus.READY:
        return 'ready'
      default:
        return ''
    }
  }

  const getOrderTypeIcon = () => {
    switch (order.orderType) {
      case OrderType.DINE_IN:
        return <HomeOutlined />
      case OrderType.TAKEAWAY:
        return <ShopOutlined />
      case OrderType.DELIVERY:
        return <CarOutlined />
      default:
        return <ShopOutlined />
    }
  }

  const getOrderTypeText = () => {
    switch (order.orderType) {
      case OrderType.DINE_IN:
        return '內用'
      case OrderType.TAKEAWAY:
        return '外帶'
      case OrderType.DELIVERY:
        return '外送'
      default:
        return '外帶'
    }
  }

  const getTimerColor = () => {
    if (order.isOverdue) return 'danger'
    if (order.remainingTime <= 5) return 'warning'
    return ''
  }

  const formatTime = (minutes: number) => {
    if (minutes < 0) return `+${Math.abs(minutes)}分`
    return `${minutes}分`
  }

  const handleStart = () => {
    startOrder(order.id)
    socketService.startOrder(order.id)
  }

  const handleComplete = () => {
    completeOrder(order.id)
    socketService.completeOrder(order.id)
  }

  const handleCancel = () => {
    setCancelModalVisible(true)
  }

  const confirmCancel = () => {
    cancelOrder(order.id, cancelReason)
    socketService.updateOrderStatus(order.id, OrderStatus.CANCELLED)
    setCancelModalVisible(false)
    setCancelReason('')
  }

  const renderActionButtons = () => {
    switch (order.status) {
      case OrderStatus.PENDING:
      case OrderStatus.CONFIRMED:
        return (
          <Space>
            <Button
              className="kds-action-btn start"
              icon={<PlayCircleOutlined />}
              onClick={handleStart}
              size="small"
            >
              開始
            </Button>
            <Button
              className="kds-action-btn cancel"
              icon={<CloseCircleOutlined />}
              onClick={handleCancel}
              size="small"
            >
              取消
            </Button>
          </Space>
        )
      
      case OrderStatus.PREPARING:
        return (
          <Space>
            <Button
              className="kds-action-btn complete"
              icon={<CheckCircleOutlined />}
              onClick={handleComplete}
              size="small"
            >
              完成
            </Button>
            <Button
              className="kds-action-btn cancel"
              icon={<CloseCircleOutlined />}
              onClick={handleCancel}
              size="small"
            >
              取消
            </Button>
          </Space>
        )
      
      case OrderStatus.READY:
        return (
          <Badge status="success" text="已完成" />
        )
      
      default:
        return null
    }
  }

  return (
    <>
      <Card className={`kds-order-card ${getStatusClass()}`}>
        {/* 訂單標題 */}
        <div className="kds-order-header">
          <div>
            <Title level={5} className="kds-order-number">
              {order.orderNumber}
            </Title>
            <Text className="kds-order-time">
              {dayjs(order.orderTime).format('HH:mm')}
            </Text>
          </div>
          
          <div className={`kds-order-timer ${getTimerColor()}`}>
            <ClockCircleOutlined style={{ marginRight: 4 }} />
            {order.isOverdue ? formatTime(-order.elapsedTime) : formatTime(order.remainingTime)}
          </div>
        </div>

        {/* 客戶信息 */}
        {(order.customerName || order.customerPhone) && (
          <div style={{ marginBottom: 12 }}>
            {order.customerName && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 4, marginBottom: 4 }}>
                <UserOutlined style={{ fontSize: 12, color: '#8c8c8c' }} />
                <Text style={{ fontSize: 12 }}>{order.customerName}</Text>
              </div>
            )}
            {order.customerPhone && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <PhoneOutlined style={{ fontSize: 12, color: '#8c8c8c' }} />
                <Text style={{ fontSize: 12 }}>{order.customerPhone}</Text>
              </div>
            )}
          </div>
        )}

        {/* 訂單項目 */}
        <div className="kds-order-items">
          {order.orderItems.map(item => (
            <div key={item.id} className="kds-order-item">
              <div>
                <div className="kds-item-name">{item.menuItem?.name}</div>
                {item.notes && (
                  <div className="kds-item-notes">{item.notes}</div>
                )}
              </div>
              <div className="kds-item-quantity">x{item.quantity}</div>
            </div>
          ))}
        </div>

        {/* 訂單備註 */}
        {order.notes && (
          <div style={{ 
            margin: '12px 0', 
            padding: '8px', 
            background: '#fff7e6', 
            borderRadius: 6,
            border: '1px solid #ffd591'
          }}>
            <Text style={{ fontSize: 12, color: '#fa8c16' }}>
              <ExclamationCircleOutlined style={{ marginRight: 4 }} />
              {order.notes}
            </Text>
          </div>
        )}

        {/* 訂單底部 */}
        <div className="kds-order-footer">
          <div className={`kds-order-type ${order.orderType.toLowerCase().replace('_', '-')}`}>
            {getOrderTypeIcon()}
            <span style={{ marginLeft: 4 }}>{getOrderTypeText()}</span>
          </div>
          
          <div className="kds-order-actions">
            {renderActionButtons()}
          </div>
        </div>
      </Card>

      {/* 取消訂單對話框 */}
      <Modal
        title="取消訂單"
        open={cancelModalVisible}
        onOk={confirmCancel}
        onCancel={() => setCancelModalVisible(false)}
        okText="確認取消"
        cancelText="返回"
        okButtonProps={{ danger: true }}
      >
        <p>確定要取消訂單 <strong>{order.orderNumber}</strong> 嗎？</p>
        <TextArea
          placeholder="請輸入取消原因（可選）"
          value={cancelReason}
          onChange={(e) => setCancelReason(e.target.value)}
          rows={3}
          style={{ marginTop: 12 }}
        />
      </Modal>
    </>
  )
}

export default OrderCard
