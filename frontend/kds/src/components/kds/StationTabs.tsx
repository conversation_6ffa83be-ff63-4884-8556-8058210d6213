import { Tabs, Badge, Button, Space } from 'antd'
import { PlusOutlined, SettingOutlined } from '@ant-design/icons'
import { useKDSStore } from '@/stores/kdsStore'

const StationTabs = () => {
  const { settings, selectedStation, setSelectedStation, getOrdersByStation } = useKDSStore()

  const handleStationChange = (stationId: string) => {
    setSelectedStation(stationId === 'all' ? null : stationId)
  }

  const tabItems = [
    {
      key: 'all',
      label: (
        <Badge count={settings.stations.reduce((sum, station) => sum + station.currentOrders, 0)}>
          全部工作站
        </Badge>
      ),
    },
    ...settings.stations.map(station => ({
      key: station.id,
      label: (
        <Badge count={getOrdersByStation(station.id).length}>
          {station.name}
        </Badge>
      ),
    })),
  ]

  const tabBarExtraContent = (
    <Space>
      <Button 
        type="text" 
        icon={<PlusOutlined />} 
        size="small"
        onClick={() => {
          // TODO: 實現添加工作站功能
        }}
      >
        添加工作站
      </Button>
      <Button 
        type="text" 
        icon={<SettingOutlined />} 
        size="small"
        onClick={() => {
          // TODO: 實現工作站設定功能
        }}
      >
        工作站設定
      </Button>
    </Space>
  )

  return (
    <div style={{ 
      background: 'white', 
      borderBottom: '1px solid #f0f0f0',
      padding: '0 16px',
    }}>
      <Tabs
        activeKey={selectedStation || 'all'}
        onChange={handleStationChange}
        items={tabItems}
        tabBarExtraContent={tabBarExtraContent}
        size="small"
      />
    </div>
  )
}

export default StationTabs
