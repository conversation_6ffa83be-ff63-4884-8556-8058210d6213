import { ReactNode, useState, useEffect } from 'react'
import { Typography, Space, Button, Badge, Dropdown, Avatar, Divider } from 'antd'
import { 
  SettingOutlined,
  LogoutOutlined,
  UserOutlined,
  ReloadOutlined,
  SoundOutlined,
  WifiOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'
import { useKDSStore } from '@/stores/kdsStore'
import { useSocket } from '@/services/socketService'
import dayjs from 'dayjs'

const { Title, Text } = Typography

interface KDSLayoutProps {
  children: ReactNode
}

const KDSLayout = ({ children }: KDSLayoutProps) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuthStore()
  const { stats, settings, lastUpdated } = useKDSStore()
  const { isConnected } = useSocket()
  const [currentTime, setCurrentTime] = useState(dayjs())

  // 更新當前時間
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(dayjs())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '個人資料',
      onClick: () => {
        // TODO: 實現個人資料頁面
      },
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '登出',
      onClick: logout,
    },
  ]

  const handleRefresh = () => {
    window.location.reload()
  }

  const toggleSound = () => {
    const { updateSettings } = useKDSStore.getState()
    updateSettings({ enableSound: !settings.enableSound })
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 頂部標題欄 */}
      <div className="kds-header">
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            廚房顯示系統
          </Title>
          
          {user?.store && (
            <>
              <Divider type="vertical" />
              <Text strong>{user.store.name}</Text>
            </>
          )}
          
          {/* 連接狀態指示器 */}
          <Badge 
            status={isConnected ? 'success' : 'error'} 
            text={isConnected ? '已連接' : '連接中斷'}
          />
        </div>

        <Space size="large">
          {/* 統計信息 */}
          <Space size="middle">
            <Badge count={stats.pending} color="#faad14">
              <Text>待處理</Text>
            </Badge>
            <Badge count={stats.preparing} color="#1890ff">
              <Text>製作中</Text>
            </Badge>
            <Badge count={stats.ready} color="#52c41a">
              <Text>已完成</Text>
            </Badge>
            {stats.overdueCount > 0 && (
              <Badge count={stats.overdueCount} color="#ff4d4f">
                <Text>超時</Text>
              </Badge>
            )}
          </Space>

          {/* 當前時間 */}
          <Space>
            <ClockCircleOutlined />
            <Text>{currentTime.format('HH:mm:ss')}</Text>
          </Space>

          {/* 最後更新時間 */}
          {lastUpdated && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              更新於 {dayjs(lastUpdated).format('HH:mm:ss')}
            </Text>
          )}

          {/* 工具按鈕 */}
          <Space>
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              title="刷新頁面"
            />
            
            <Button
              type="text"
              icon={<SoundOutlined />}
              onClick={toggleSound}
              title={settings.enableSound ? '關閉聲音' : '開啟聲音'}
              style={{ 
                color: settings.enableSound ? '#1890ff' : '#d9d9d9' 
              }}
            />

            {location.pathname !== '/settings' && (
              <Button
                type="text"
                icon={<SettingOutlined />}
                onClick={() => navigate('/settings')}
                title="系統設定"
              />
            )}

            {/* 連接狀態 */}
            <WifiOutlined 
              style={{ 
                color: isConnected ? '#52c41a' : '#ff4d4f',
                fontSize: 16 
              }}
              title={isConnected ? '已連接到服務器' : '連接中斷'}
            />

            {/* 用戶菜單 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Button type="text" style={{ height: 'auto', padding: '4px 8px' }}>
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  <Text>{user?.name}</Text>
                </Space>
              </Button>
            </Dropdown>
          </Space>
        </Space>
      </div>

      {/* 主要內容區域 */}
      <div className="kds-main">
        {children}
      </div>
    </div>
  )
}

export default KDSLayout
