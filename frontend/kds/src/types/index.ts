// 基礎類型（與 POS 共享）
export interface User {
  id: string
  email: string
  username: string
  name: string
  role: UserRole
  storeId?: string
  store?: Store
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  CASHIER = 'CASHIER',
  KITCHEN = 'KITCHEN',
}

export interface Store {
  id: string
  name: string
  address?: string
  phone?: string
  email?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  name: string
  description?: string
  sortOrder: number
  isActive: boolean
  storeId: string
  createdAt: string
  updatedAt: string
}

export interface MenuItem {
  id: string
  name: string
  description?: string
  price: number
  cost?: number
  image?: string
  isActive: boolean
  isAvailable: boolean
  categoryId: string
  category?: Category
  storeId: string
  stock?: number
  lowStockAlert?: number
  prepTime?: number
  createdAt: string
  updatedAt: string
}

export interface Order {
  id: string
  orderNumber: string
  status: OrderStatus
  customerName?: string
  customerPhone?: string
  subtotal: number
  tax: number
  discount: number
  total: number
  paymentMethod?: PaymentMethod
  paymentStatus: PaymentStatus
  paidAt?: string
  orderType: OrderType
  notes?: string
  orderTime: string
  estimatedTime?: string
  completedAt?: string
  storeId: string
  store?: Store
  userId: string
  user?: User
  orderItems: OrderItem[]
  createdAt: string
  updatedAt: string
}

export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
}

export enum PaymentMethod {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  MOBILE_PAYMENT = 'MOBILE_PAYMENT',
  BANK_TRANSFER = 'BANK_TRANSFER',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

export enum OrderType {
  DINE_IN = 'DINE_IN',
  TAKEAWAY = 'TAKEAWAY',
  DELIVERY = 'DELIVERY',
}

export interface OrderItem {
  id: string
  quantity: number
  unitPrice: number
  totalPrice: number
  notes?: string
  orderId: string
  menuItemId: string
  menuItem?: MenuItem
  createdAt: string
  updatedAt: string
}

// KDS 特定類型
export interface KDSOrder extends Order {
  // 計算屬性
  elapsedTime: number // 已經過時間（分鐘）
  remainingTime: number // 剩餘時間（分鐘）
  isOverdue: boolean // 是否超時
  priority: OrderPriority // 優先級
  station?: KitchenStation // 分配的廚房工作站
}

export enum OrderPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export interface KitchenStation {
  id: string
  name: string
  description?: string
  isActive: boolean
  maxOrders: number // 最大同時處理訂單數
  currentOrders: number // 當前處理訂單數
  categories: string[] // 負責的菜品分類
}

// KDS 設定
export interface KDSSettings {
  // 時間設定
  defaultPrepTime: number // 預設製作時間（分鐘）
  warningTime: number // 警告時間（分鐘）
  overdueTime: number // 超時時間（分鐘）
  
  // 顯示設定
  ordersPerPage: number // 每頁顯示訂單數
  autoRefreshInterval: number // 自動刷新間隔（秒）
  showCompletedOrders: boolean // 是否顯示已完成訂單
  completedOrderDisplayTime: number // 已完成訂單顯示時間（秒）
  
  // 聲音設定
  enableSound: boolean // 是否啟用聲音
  newOrderSound: boolean // 新訂單聲音
  overdueSound: boolean // 超時聲音
  soundVolume: number // 音量（0-100）
  
  // 工作站設定
  enableStations: boolean // 是否啟用工作站
  stations: KitchenStation[]
}

// 訂單統計
export interface OrderStats {
  total: number
  pending: number
  preparing: number
  ready: number
  completed: number
  cancelled: number
  averagePrepTime: number
  overdueCount: number
}

// API 響應類型
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: any[]
}

// Socket.IO 事件類型（KDS 特定）
export interface KDSSocketEvents {
  // 連接事件
  connection: void
  disconnect: void
  authenticate: { token: string }
  authenticated: { userId: string; userRole: string; storeId?: string }
  
  // 訂單事件
  kds_order_received: Order
  kds_order_updated: Order
  kds_order_started: { orderId: string; stationId?: string }
  kds_order_completed: { orderId: string; completedAt: string }
  kds_order_cancelled: { orderId: string; reason?: string }
  
  // 狀態更新事件
  order_status_changed: { orderId: string; status: OrderStatus; userId: string }
  
  // 系統事件
  kds_settings_updated: KDSSettings
  station_status_changed: { stationId: string; isActive: boolean; currentOrders: number }
  
  // 通知事件
  order_overdue: { orderId: string; elapsedTime: number }
  low_stock_alert: { itemId: string; itemName: string; currentStock: number }
}

// 錯誤類型
export interface AppError {
  message: string
  code?: string
  statusCode?: number
  details?: any
}

// 認證相關類型
export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  user: User
  token: string
  refreshToken: string
}

// 訂單操作類型
export interface OrderAction {
  type: 'START' | 'COMPLETE' | 'CANCEL' | 'ASSIGN_STATION'
  orderId: string
  stationId?: string
  notes?: string
  timestamp: string
}
