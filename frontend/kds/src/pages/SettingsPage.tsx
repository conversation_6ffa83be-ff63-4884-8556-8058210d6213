import { useState } from 'react'
import { Card, Form, InputNumber, Switch, Button, Space, Divider, Typography, Slider, message } from 'antd'
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons'
import { useKDSStore } from '@/stores/kdsStore'
import { KDSSettings } from '@/types'

const { Title, Text } = Typography

const SettingsPage = () => {
  const { settings, updateSettings } = useKDSStore()
  const [form] = Form.useForm()
  const [hasChanges, setHasChanges] = useState(false)

  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      updateSettings(values)
      setHasChanges(false)
      message.success('設定已保存')
    } catch (error) {
      message.error('保存失敗，請檢查輸入')
    }
  }

  const handleReset = () => {
    form.setFieldsValue(settings)
    setHasChanges(false)
    message.info('已重置為當前設定')
  }

  const handleValuesChange = () => {
    setHasChanges(true)
  }

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>KDS 系統設定</Title>
        <Text type="secondary">配置廚房顯示系統的各項參數</Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onValuesChange={handleValuesChange}
      >
        {/* 時間設定 */}
        <Card title="時間設定" style={{ marginBottom: 16 }}>
          <Form.Item
            name="defaultPrepTime"
            label="預設製作時間（分鐘）"
            help="當菜品沒有設定製作時間時使用的預設值"
          >
            <InputNumber min={1} max={60} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="warningTime"
            label="警告時間（分鐘）"
            help="剩餘時間少於此值時顯示警告"
          >
            <InputNumber min={1} max={30} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="overdueTime"
            label="超時時間（分鐘）"
            help="超過預計完成時間多少分鐘後標記為超時"
          >
            <InputNumber min={1} max={60} style={{ width: '100%' }} />
          </Form.Item>
        </Card>

        {/* 顯示設定 */}
        <Card title="顯示設定" style={{ marginBottom: 16 }}>
          <Form.Item
            name="ordersPerPage"
            label="每頁顯示訂單數"
            help="同時在螢幕上顯示的最大訂單數量"
          >
            <InputNumber min={4} max={20} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="autoRefreshInterval"
            label="自動刷新間隔（秒）"
            help="系統自動更新訂單狀態的時間間隔"
          >
            <InputNumber min={10} max={300} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="showCompletedOrders"
            label="顯示已完成訂單"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="completedOrderDisplayTime"
            label="已完成訂單顯示時間（秒）"
            help="已完成的訂單在螢幕上保留多長時間，0表示不自動移除"
          >
            <InputNumber min={0} max={600} style={{ width: '100%' }} />
          </Form.Item>
        </Card>

        {/* 聲音設定 */}
        <Card title="聲音設定" style={{ marginBottom: 16 }}>
          <Form.Item
            name="enableSound"
            label="啟用聲音提醒"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="newOrderSound"
            label="新訂單聲音"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="overdueSound"
            label="超時警告聲音"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="soundVolume"
            label="音量"
          >
            <Slider
              min={0}
              max={100}
              marks={{
                0: '靜音',
                50: '50%',
                100: '最大',
              }}
            />
          </Form.Item>
        </Card>

        {/* 工作站設定 */}
        <Card title="工作站設定" style={{ marginBottom: 16 }}>
          <Form.Item
            name="enableStations"
            label="啟用工作站功能"
            valuePropName="checked"
            help="啟用後可以將訂單分配到不同的工作站"
          >
            <Switch />
          </Form.Item>

          <div style={{ 
            padding: 16, 
            background: '#f6f8fa', 
            borderRadius: 6,
            marginTop: 16 
          }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              <strong>工作站管理：</strong><br />
              工作站功能允許您將廚房分為不同的區域（如熱食區、飲品區、甜點區），
              每個工作站可以獨立處理分配給它的訂單。這有助於提高廚房效率和訂單管理。
            </Text>
          </div>
        </Card>

        {/* 操作按鈕 */}
        <Card>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              disabled={!hasChanges}
            >
              保存設定
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              disabled={!hasChanges}
            >
              重置
            </Button>
          </Space>
          
          {hasChanges && (
            <div style={{ marginTop: 12 }}>
              <Text type="warning">您有未保存的更改</Text>
            </div>
          )}
        </Card>
      </Form>
    </div>
  )
}

export default SettingsPage
