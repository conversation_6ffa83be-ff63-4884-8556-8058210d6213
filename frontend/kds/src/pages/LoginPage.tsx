import { useState } from 'react'
import { Form, Input, Button, Card, Typography, Space, Divider } from 'antd'
import { UserOutlined, LockOutlined, CookieOutlined } from '@ant-design/icons'
import { useAuthStore } from '@/stores/authStore'
import { LoginRequest } from '@/types'

const { Title, Text } = Typography

const LoginPage = () => {
  const [form] = Form.useForm()
  const { login, isLoading } = useAuthStore()
  const [loginType, setLoginType] = useState<'manual' | 'demo'>('manual')

  const handleLogin = async (values: LoginRequest) => {
    const success = await login(values)
    if (success) {
      // 登入成功後會自動重定向到主頁
    }
  }

  const handleDemoLogin = async (role: 'admin' | 'kitchen') => {
    const demoAccounts = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      kitchen: { email: '<EMAIL>', password: 'kitchen123' },
    }

    await login(demoAccounts[role])
  }

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <CookieOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            廚房顯示系統
          </Title>
          <Text type="secondary">KDS - Kitchen Display System</Text>
        </div>

        {loginType === 'manual' ? (
          <Form
            form={form}
            name="login"
            onFinish={handleLogin}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              label="電子郵件"
              rules={[
                { required: true, message: '請輸入電子郵件' },
                { type: 'email', message: '請輸入有效的電子郵件格式' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="請輸入電子郵件"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密碼"
              rules={[
                { required: true, message: '請輸入密碼' },
                { min: 6, message: '密碼至少需要6個字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="請輸入密碼"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                block
                style={{ height: 48 }}
              >
                登入廚房系統
              </Button>
            </Form.Item>
          </Form>
        ) : (
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <Button
              type="primary"
              block
              size="large"
              loading={isLoading}
              onClick={() => handleDemoLogin('admin')}
            >
              管理員登入
            </Button>
            <Button
              block
              size="large"
              loading={isLoading}
              onClick={() => handleDemoLogin('kitchen')}
            >
              廚房人員登入
            </Button>
          </Space>
        )}

        <Divider>或</Divider>

        <Button
          type="link"
          block
          onClick={() => setLoginType(loginType === 'manual' ? 'demo' : 'manual')}
        >
          {loginType === 'manual' ? '使用演示帳號' : '手動輸入帳號'}
        </Button>

        {loginType === 'demo' && (
          <div style={{ marginTop: 16, padding: 16, background: '#f6f8fa', borderRadius: 6 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              <strong>KDS 演示帳號：</strong><br />
              • 管理員：可以訪問所有功能和設定<br />
              • 廚房人員：專門用於廚房訂單處理
            </Text>
          </div>
        )}
      </Card>
    </div>
  )
}

export default LoginPage
