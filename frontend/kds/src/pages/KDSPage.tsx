import { useEffect, useState } from 'react'
import { Row, Col, Spin, Alert, Empty } from 'antd'
import { CookieOutlined } from '@ant-design/icons'
import { useKDSStore } from '@/stores/kdsStore'
import { useAuthStore } from '@/stores/authStore'
import { OrderStatus, KDSOrder } from '@/types'

// 組件導入
import OrderCard from '@/components/kds/OrderCard'
import StationTabs from '@/components/kds/StationTabs'
import OrderFilters from '@/components/kds/OrderFilters'

const KDSPage = () => {
  const { user } = useAuthStore()
  const { 
    orders, 
    selectedStation, 
    settings, 
    isLoading, 
    updateOrderTimes,
    getOrdersByStatus,
    getOrdersByStation 
  } = useKDSStore()
  
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus[]>([
    OrderStatus.CONFIRMED,
    OrderStatus.PREPARING,
    OrderStatus.READY
  ])

  // 定期更新訂單時間
  useEffect(() => {
    const interval = setInterval(() => {
      updateOrderTimes()
    }, 30000) // 每30秒更新一次

    return () => clearInterval(interval)
  }, [updateOrderTimes])

  // 模擬數據載入
  useEffect(() => {
    // TODO: 實際從 API 載入訂單數據
    const mockOrders: KDSOrder[] = [
      {
        id: '1',
        orderNumber: 'ORD20250803001',
        status: OrderStatus.CONFIRMED,
        customerName: '張先生',
        customerPhone: '0912345678',
        subtotal: 180,
        tax: 9,
        discount: 0,
        total: 189,
        paymentStatus: 'PAID' as any,
        orderType: 'DINE_IN' as any,
        notes: '不要洋蔥',
        orderTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5分鐘前
        storeId: user?.storeId || 'store-1',
        userId: user?.id || 'user-1',
        orderItems: [
          {
            id: '1',
            quantity: 1,
            unitPrice: 120,
            totalPrice: 120,
            orderId: '1',
            menuItemId: '1',
            menuItem: {
              id: '1',
              name: '經典漢堡',
              description: '牛肉漢堡配生菜、番茄、起司',
              price: 120,
              isActive: true,
              isAvailable: true,
              categoryId: '1',
              storeId: 'store-1',
              prepTime: 8,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: '2',
            quantity: 1,
            unitPrice: 60,
            totalPrice: 60,
            notes: '去冰',
            orderId: '1',
            menuItemId: '2',
            menuItem: {
              id: '2',
              name: '美式咖啡',
              description: '香濃美式咖啡',
              price: 60,
              isActive: true,
              isAvailable: true,
              categoryId: '2',
              storeId: 'store-1',
              prepTime: 3,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        elapsedTime: 5,
        remainingTime: 6,
        isOverdue: false,
        priority: 'NORMAL' as any,
      },
      {
        id: '2',
        orderNumber: 'ORD20250803002',
        status: OrderStatus.PREPARING,
        customerName: '李小姐',
        subtotal: 80,
        tax: 4,
        discount: 0,
        total: 84,
        paymentStatus: 'PAID' as any,
        orderType: 'TAKEAWAY' as any,
        orderTime: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15分鐘前
        storeId: user?.storeId || 'store-1',
        userId: user?.id || 'user-1',
        orderItems: [
          {
            id: '3',
            quantity: 1,
            unitPrice: 80,
            totalPrice: 80,
            orderId: '2',
            menuItemId: '3',
            menuItem: {
              id: '3',
              name: '火腿三明治',
              description: '新鮮火腿配生菜、番茄',
              price: 80,
              isActive: true,
              isAvailable: true,
              categoryId: '1',
              storeId: 'store-1',
              prepTime: 5,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        elapsedTime: 15,
        remainingTime: 0,
        isOverdue: true,
        priority: 'URGENT' as any,
      },
    ]

    // 模擬載入延遲
    setTimeout(() => {
      useKDSStore.getState().setOrders(mockOrders)
    }, 1000)
  }, [user])

  // 過濾訂單
  const filteredOrders = orders.filter(order => {
    // 按狀態過濾
    if (!selectedStatus.includes(order.status)) {
      return false
    }

    // 按工作站過濾
    if (selectedStation && order.station?.id !== selectedStation) {
      return false
    }

    return true
  })

  if (isLoading) {
    return (
      <div className="kds-loading">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工作站標籤（如果啟用） */}
      {settings.enableStations && settings.stations.length > 0 && (
        <StationTabs />
      )}

      {/* 訂單過濾器 */}
      <OrderFilters 
        selectedStatus={selectedStatus}
        onStatusChange={setSelectedStatus}
      />

      {/* 訂單網格 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        {filteredOrders.length === 0 ? (
          <Empty
            className="kds-empty"
            image={<CookieOutlined className="kds-empty-icon" />}
            description={
              <div>
                <div className="kds-empty-text">暫無訂單</div>
                <div className="kds-empty-description">
                  等待新的訂單到達...
                </div>
              </div>
            }
          />
        ) : (
          <div className="kds-order-grid">
            {filteredOrders.map(order => (
              <OrderCard key={order.id} order={order} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default KDSPage
