/* 重置樣式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f0f2f5;
}

#root {
  height: 100%;
}

/* KDS 專用樣式 */
.kds-container {
  height: 100vh;
  overflow: hidden;
  background: #f0f2f5;
}

.kds-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.kds-main {
  height: calc(100vh - 80px);
  padding: 16px;
  overflow-y: auto;
}

/* 訂單卡片樣式 */
.kds-order-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 0;
}

.kds-order-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #d9d9d9;
  transition: all 0.3s ease;
  position: relative;
  min-height: 200px;
}

.kds-order-card.pending {
  border-left-color: #faad14;
  background: #fffbe6;
}

.kds-order-card.preparing {
  border-left-color: #1890ff;
  background: #e6f7ff;
}

.kds-order-card.ready {
  border-left-color: #52c41a;
  background: #f6ffed;
}

.kds-order-card.overdue {
  border-left-color: #ff4d4f;
  background: #fff2f0;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
  50% { box-shadow: 0 4px 16px rgba(255, 77, 79, 0.3); }
  100% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
}

.kds-order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.kds-order-number {
  font-size: 18px;
  font-weight: bold;
  color: #262626;
}

.kds-order-time {
  font-size: 14px;
  color: #8c8c8c;
}

.kds-order-timer {
  font-size: 16px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 6px;
  background: #f0f0f0;
  color: #595959;
}

.kds-order-timer.warning {
  background: #fff7e6;
  color: #fa8c16;
}

.kds-order-timer.danger {
  background: #fff2f0;
  color: #ff4d4f;
}

.kds-order-items {
  margin: 12px 0;
}

.kds-order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.kds-order-item:last-child {
  border-bottom: none;
}

.kds-item-name {
  font-weight: 500;
  color: #262626;
}

.kds-item-quantity {
  background: #f0f0f0;
  color: #595959;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.kds-item-notes {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  font-style: italic;
}

.kds-order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.kds-order-type {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.kds-order-type.dine-in {
  background: #e6f7ff;
  color: #1890ff;
}

.kds-order-type.takeaway {
  background: #fff7e6;
  color: #fa8c16;
}

.kds-order-type.delivery {
  background: #f6ffed;
  color: #52c41a;
}

.kds-order-actions {
  display: flex;
  gap: 8px;
}

.kds-action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.kds-action-btn.start {
  background: #1890ff;
  color: white;
}

.kds-action-btn.start:hover {
  background: #40a9ff;
}

.kds-action-btn.complete {
  background: #52c41a;
  color: white;
}

.kds-action-btn.complete:hover {
  background: #73d13d;
}

.kds-action-btn.cancel {
  background: #ff4d4f;
  color: white;
}

.kds-action-btn.cancel:hover {
  background: #ff7875;
}

/* 統計面板樣式 */
.kds-stats-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.kds-stat-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.kds-stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.kds-stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

/* 工作站樣式 */
.kds-stations {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.kds-station {
  min-width: 200px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.kds-station.active {
  border-color: #1890ff;
  background: #e6f7ff;
}

.kds-station-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.kds-station-orders {
  font-size: 14px;
  color: #8c8c8c;
}

/* 響應式設計 */
@media (max-width: 1200px) {
  .kds-order-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .kds-order-grid {
    grid-template-columns: 1fr;
  }
  
  .kds-main {
    padding: 12px;
  }
  
  .kds-header {
    padding: 12px 16px;
  }
  
  .kds-stats-panel {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 載入動畫 */
.kds-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.kds-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f0f0f0;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空狀態樣式 */
.kds-empty {
  text-align: center;
  padding: 60px 20px;
  color: #8c8c8c;
}

.kds-empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.kds-empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.kds-empty-description {
  font-size: 14px;
  opacity: 0.7;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
