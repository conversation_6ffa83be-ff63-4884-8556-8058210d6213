import { get, post } from './api'
import { LoginRequest, LoginResponse, User, ApiResponse } from '@/types'

export const authService = {
  // 登入
  login: (credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    return post<LoginResponse>('/auth/login', credentials)
  },

  // 登出
  logout: (): Promise<ApiResponse> => {
    return post('/auth/logout')
  },

  // 刷新 token
  refreshToken: (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    return post('/auth/refresh', { refreshToken })
  },

  // 獲取當前用戶信息
  getCurrentUser: (): Promise<ApiResponse<{ user: User }>> => {
    return get('/auth/me')
  },

  // 修改密碼
  changePassword: (data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<ApiResponse> => {
    return post('/auth/change-password', data)
  },
}
