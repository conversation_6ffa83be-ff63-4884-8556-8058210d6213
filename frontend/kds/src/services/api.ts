import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ApiResponse } from '@/types'
import { useAuthStore } from '@/stores/authStore'
import toast from 'react-hot-toast'

// 創建 axios 實例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 請求攔截器
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 響應攔截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config
    
    // 處理 401 錯誤（未授權）
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      const { refreshAuth, logout } = useAuthStore.getState()
      
      try {
        const success = await refreshAuth()
        
        if (success) {
          // 重新發送原始請求
          const token = useAuthStore.getState().token
          originalRequest.headers.Authorization = `Bearer ${token}`
          return api(originalRequest)
        } else {
          logout()
          window.location.href = '/login'
        }
      } catch (refreshError) {
        logout()
        window.location.href = '/login'
      }
    }
    
    // 處理其他錯誤
    const errorMessage = error.response?.data?.message || error.message || '請求失敗'
    
    // 不顯示某些特定錯誤的 toast
    const silentErrors = [401, 403]
    if (!silentErrors.includes(error.response?.status)) {
      toast.error(errorMessage)
    }
    
    return Promise.reject(error)
  }
)

// API 請求包裝器
export const apiRequest = async <T = any>(
  config: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    const response = await api(config)
    return response.data
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data
    }
    
    return {
      success: false,
      message: error.message || '網路錯誤',
    }
  }
}

// GET 請求
export const get = <T = any>(
  url: string,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  return apiRequest<T>({ ...config, method: 'GET', url })
}

// POST 請求
export const post = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  return apiRequest<T>({ ...config, method: 'POST', url, data })
}

// PUT 請求
export const put = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  return apiRequest<T>({ ...config, method: 'PUT', url, data })
}

// DELETE 請求
export const del = <T = any>(
  url: string,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  return apiRequest<T>({ ...config, method: 'DELETE', url })
}

// PATCH 請求
export const patch = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  return apiRequest<T>({ ...config, method: 'PATCH', url, data })
}

export default api
