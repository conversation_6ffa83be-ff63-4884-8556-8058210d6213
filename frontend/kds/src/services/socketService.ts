import { io, Socket } from 'socket.io-client'
import { KDSSocketEvents, Order, OrderStatus } from '@/types'
import { useAuthStore } from '@/stores/authStore'
import { useKDSStore } from '@/stores/kdsStore'
import toast from 'react-hot-toast'

class SocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  connect() {
    const token = useAuthStore.getState().token
    
    if (!token) {
      console.warn('No auth token available for socket connection')
      return
    }

    const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001'
    
    this.socket = io(socketUrl, {
      auth: { token },
      transports: ['websocket', 'polling'],
      timeout: 10000,
    })

    this.setupEventListeners()
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  private setupEventListeners() {
    if (!this.socket) return

    // 連接事件
    this.socket.on('connect', () => {
      console.log('Socket connected')
      this.reconnectAttempts = 0
      toast.success('已連接到服務器')
    })

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
      toast.error('與服務器連接中斷')
      
      if (reason === 'io server disconnect') {
        // 服務器主動斷開，需要重新連接
        this.handleReconnect()
      }
    })

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      this.handleReconnect()
    })

    // 認證事件
    this.socket.on('authenticated', (data) => {
      console.log('Socket authenticated:', data)
    })

    this.socket.on('authentication_error', (error) => {
      console.error('Socket authentication error:', error)
      toast.error('認證失敗，請重新登入')
      useAuthStore.getState().logout()
    })

    // KDS 特定事件
    this.socket.on('kds_order_received', (order: Order) => {
      console.log('New order received:', order)
      useKDSStore.getState().addOrder(order as any)
    })

    this.socket.on('kds_order_updated', (order: Order) => {
      console.log('Order updated:', order)
      useKDSStore.getState().updateOrder(order.id, order as any)
    })

    this.socket.on('order_status_changed', (data: { orderId: string; status: OrderStatus; userId: string }) => {
      console.log('Order status changed:', data)
      useKDSStore.getState().updateOrder(data.orderId, { status: data.status })
    })

    // 系統事件
    this.socket.on('system_notification', (notification) => {
      console.log('System notification:', notification)
      
      switch (notification.type) {
        case 'info':
          toast.success(notification.message)
          break
        case 'warning':
          toast.error(notification.message)
          break
        case 'error':
          toast.error(notification.message)
          break
        default:
          toast(notification.message)
      }
    })

    this.socket.on('inventory_low_stock', (data) => {
      console.log('Low stock alert:', data)
      toast.error(`庫存不足警告：${data.itemName} (剩餘 ${data.currentStock})`)
    })

    this.socket.on('inventory_out_of_stock', (data) => {
      console.log('Out of stock alert:', data)
      toast.error(`商品缺貨：${data.itemName}`)
    })
  }

  private handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      toast.error('無法連接到服務器，請檢查網路連接')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`)
    
    setTimeout(() => {
      if (this.socket) {
        this.socket.connect()
      }
    }, delay)
  }

  // 發送事件的方法
  startOrder(orderId: string, stationId?: string) {
    if (this.socket) {
      this.socket.emit('kds_order_started', { orderId, stationId })
    }
  }

  completeOrder(orderId: string) {
    if (this.socket) {
      this.socket.emit('kds_order_completed', { 
        orderId, 
        completedAt: new Date().toISOString() 
      })
    }
  }

  updateOrderStatus(orderId: string, status: OrderStatus) {
    if (this.socket) {
      this.socket.emit('order_status_changed', { 
        orderId, 
        status, 
        userId: useAuthStore.getState().user?.id 
      })
    }
  }

  // 加入特定房間
  joinKitchenRoom(storeId: string) {
    if (this.socket) {
      this.socket.emit('join_kitchen', { storeId })
    }
  }

  // 離開房間
  leaveKitchenRoom(storeId: string) {
    if (this.socket) {
      this.socket.emit('leave_kitchen', { storeId })
    }
  }

  // 檢查連接狀態
  isConnected(): boolean {
    return this.socket?.connected || false
  }

  // 獲取 socket 實例（用於其他自定義事件）
  getSocket(): Socket | null {
    return this.socket
  }
}

// 創建單例實例
export const socketService = new SocketService()

// 自動連接和斷開的 Hook
export const useSocket = () => {
  const { isAuthenticated, user } = useAuthStore()

  const connect = () => {
    if (isAuthenticated && !socketService.isConnected()) {
      socketService.connect()
      
      // 如果用戶有門店，加入廚房房間
      if (user?.storeId) {
        setTimeout(() => {
          socketService.joinKitchenRoom(user.storeId!)
        }, 1000)
      }
    }
  }

  const disconnect = () => {
    if (user?.storeId) {
      socketService.leaveKitchenRoom(user.storeId)
    }
    socketService.disconnect()
  }

  return {
    connect,
    disconnect,
    isConnected: socketService.isConnected(),
    socket: socketService.getSocket(),
  }
}
