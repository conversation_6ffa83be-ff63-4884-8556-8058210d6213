import { get, put, post } from './api'
import { Order, OrderStatus, ApiResponse, PaginationParams, PaginatedResponse } from '@/types'

export const orderService = {
  // 獲取訂單列表
  getOrders: (params?: PaginationParams & {
    status?: OrderStatus[]
    storeId?: string
  }): Promise<ApiResponse<PaginatedResponse<Order>>> => {
    return get('/orders', { params })
  },

  // 獲取單個訂單
  getOrder: (orderId: string): Promise<ApiResponse<{ order: Order }>> => {
    return get(`/orders/${orderId}`)
  },

  // 更新訂單狀態
  updateOrderStatus: (orderId: string, status: OrderStatus, notes?: string): Promise<ApiResponse<{ order: Order }>> => {
    return put(`/orders/${orderId}/status`, { status, notes })
  },

  // 開始製作訂單
  startOrder: (orderId: string, stationId?: string): Promise<ApiResponse<{ order: Order }>> => {
    return put(`/orders/${orderId}/status`, { 
      status: OrderStatus.PREPARING,
      stationId,
      startedAt: new Date().toISOString()
    })
  },

  // 完成訂單
  completeOrder: (orderId: string): Promise<ApiResponse<{ order: Order }>> => {
    return put(`/orders/${orderId}/status`, { 
      status: OrderStatus.READY,
      completedAt: new Date().toISOString()
    })
  },

  // 取消訂單
  cancelOrder: (orderId: string, reason?: string): Promise<ApiResponse<{ order: Order }>> => {
    return put(`/orders/${orderId}/status`, { 
      status: OrderStatus.CANCELLED,
      cancelReason: reason,
      cancelledAt: new Date().toISOString()
    })
  },

  // 獲取廚房訂單（僅顯示需要製作的訂單）
  getKitchenOrders: (storeId?: string): Promise<ApiResponse<{ orders: Order[] }>> => {
    return get('/orders/kitchen', { 
      params: { 
        storeId,
        status: [OrderStatus.CONFIRMED, OrderStatus.PREPARING, OrderStatus.READY]
      }
    })
  },

  // 批量更新訂單狀態
  batchUpdateOrders: (updates: Array<{
    orderId: string
    status: OrderStatus
    notes?: string
  }>): Promise<ApiResponse<{ orders: Order[] }>> => {
    return post('/orders/batch-update', { updates })
  },
}
