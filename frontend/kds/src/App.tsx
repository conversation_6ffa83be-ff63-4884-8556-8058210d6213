import { Routes, Route, Navigate } from 'react-router-dom'
import { useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { useSocket } from '@/services/socketService'

// 頁面組件
import LoginPage from '@/pages/LoginPage'
import KDSPage from '@/pages/KDSPage'
import SettingsPage from '@/pages/SettingsPage'

// 佈局組件
import KDSLayout from '@/components/layout/KDSLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

function App() {
  const { isAuthenticated, initializeAuth } = useAuthStore()
  const { connect, disconnect } = useSocket()

  useEffect(() => {
    initializeAuth()
  }, [initializeAuth])

  useEffect(() => {
    if (isAuthenticated) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [isAuthenticated, connect, disconnect])

  return (
    <div className="kds-container">
      <Routes>
        {/* 登入頁面 */}
        <Route 
          path="/login" 
          element={
            isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
          } 
        />
        
        {/* 受保護的路由 */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <KDSLayout>
                <Routes>
                  <Route path="/" element={<KDSPage />} />
                  <Route path="/settings" element={<SettingsPage />} />
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </KDSLayout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  )
}

export default App
