import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { KDSOrder, OrderStatus, KDSSettings, KitchenStation, OrderStats, OrderPriority } from '@/types'
import dayjs from 'dayjs'
import toast from 'react-hot-toast'

interface KDSState {
  // 訂單數據
  orders: KDSOrder[]
  selectedStation: string | null
  
  // 設定
  settings: KDSSettings
  
  // 統計
  stats: OrderStats
  
  // UI 狀態
  isLoading: boolean
  lastUpdated: string | null
  
  // Actions
  setOrders: (orders: KDSOrder[]) => void
  addOrder: (order: KDSOrder) => void
  updateOrder: (orderId: string, updates: Partial<KDSOrder>) => void
  removeOrder: (orderId: string) => void
  
  // 訂單操作
  startOrder: (orderId: string, stationId?: string) => void
  completeOrder: (orderId: string) => void
  cancelOrder: (orderId: string, reason?: string) => void
  
  // 工作站管理
  setSelectedStation: (stationId: string | null) => void
  updateStationOrders: (stationId: string, orderCount: number) => void
  
  // 設定管理
  updateSettings: (settings: Partial<KDSSettings>) => void
  
  // 統計計算
  calculateStats: () => void
  
  // 工具方法
  getOrdersByStatus: (status: OrderStatus) => KDSOrder[]
  getOrdersByStation: (stationId: string) => KDSOrder[]
  getOverdueOrders: () => KDSOrder[]
  updateOrderTimes: () => void
}

// 預設設定
const defaultSettings: KDSSettings = {
  defaultPrepTime: 15,
  warningTime: 10,
  overdueTime: 20,
  ordersPerPage: 12,
  autoRefreshInterval: 30,
  showCompletedOrders: true,
  completedOrderDisplayTime: 300,
  enableSound: true,
  newOrderSound: true,
  overdueSound: true,
  soundVolume: 50,
  enableStations: false,
  stations: [],
}

export const useKDSStore = create<KDSState>()(
  persist(
    (set, get) => ({
      orders: [],
      selectedStation: null,
      settings: defaultSettings,
      stats: {
        total: 0,
        pending: 0,
        preparing: 0,
        ready: 0,
        completed: 0,
        cancelled: 0,
        averagePrepTime: 0,
        overdueCount: 0,
      },
      isLoading: false,
      lastUpdated: null,

      setOrders: (orders: KDSOrder[]) => {
        set({ orders, lastUpdated: dayjs().toISOString() })
        get().calculateStats()
        get().updateOrderTimes()
      },

      addOrder: (order: KDSOrder) => {
        const { orders, settings } = get()
        
        // 計算訂單優先級和時間
        const enhancedOrder = enhanceOrder(order, settings)
        
        set({ 
          orders: [enhancedOrder, ...orders],
          lastUpdated: dayjs().toISOString()
        })
        
        get().calculateStats()
        
        // 播放新訂單聲音
        if (settings.enableSound && settings.newOrderSound) {
          playSound('new-order')
        }
        
        toast.success(`新訂單：${order.orderNumber}`)
      },

      updateOrder: (orderId: string, updates: Partial<KDSOrder>) => {
        const { orders } = get()
        
        set({
          orders: orders.map(order => 
            order.id === orderId 
              ? { ...order, ...updates }
              : order
          ),
          lastUpdated: dayjs().toISOString()
        })
        
        get().calculateStats()
      },

      removeOrder: (orderId: string) => {
        const { orders } = get()
        
        set({
          orders: orders.filter(order => order.id !== orderId),
          lastUpdated: dayjs().toISOString()
        })
        
        get().calculateStats()
      },

      startOrder: (orderId: string, stationId?: string) => {
        const { orders } = get()
        const order = orders.find(o => o.id === orderId)
        
        if (order) {
          const updates: Partial<KDSOrder> = {
            status: OrderStatus.PREPARING,
            station: stationId ? { 
              id: stationId, 
              name: '', 
              isActive: true, 
              maxOrders: 0, 
              currentOrders: 0, 
              categories: [] 
            } : undefined,
          }
          
          get().updateOrder(orderId, updates)
          toast.success(`開始製作訂單：${order.orderNumber}`)
        }
      },

      completeOrder: (orderId: string) => {
        const { orders } = get()
        const order = orders.find(o => o.id === orderId)
        
        if (order) {
          const updates: Partial<KDSOrder> = {
            status: OrderStatus.READY,
            completedAt: dayjs().toISOString(),
          }
          
          get().updateOrder(orderId, updates)
          toast.success(`訂單完成：${order.orderNumber}`)
          
          // 設定自動移除已完成訂單
          const { settings } = get()
          if (settings.completedOrderDisplayTime > 0) {
            setTimeout(() => {
              get().removeOrder(orderId)
            }, settings.completedOrderDisplayTime * 1000)
          }
        }
      },

      cancelOrder: (orderId: string, reason?: string) => {
        const { orders } = get()
        const order = orders.find(o => o.id === orderId)
        
        if (order) {
          const updates: Partial<KDSOrder> = {
            status: OrderStatus.CANCELLED,
            notes: reason ? `${order.notes || ''}\n取消原因：${reason}`.trim() : order.notes,
          }
          
          get().updateOrder(orderId, updates)
          toast.error(`訂單已取消：${order.orderNumber}`)
        }
      },

      setSelectedStation: (stationId: string | null) => {
        set({ selectedStation: stationId })
      },

      updateStationOrders: (stationId: string, orderCount: number) => {
        const { settings } = get()
        
        const updatedStations = settings.stations.map(station =>
          station.id === stationId
            ? { ...station, currentOrders: orderCount }
            : station
        )
        
        get().updateSettings({ stations: updatedStations })
      },

      updateSettings: (newSettings: Partial<KDSSettings>) => {
        const { settings } = get()
        set({ settings: { ...settings, ...newSettings } })
      },

      calculateStats: () => {
        const { orders } = get()
        
        const stats: OrderStats = {
          total: orders.length,
          pending: orders.filter(o => o.status === OrderStatus.PENDING).length,
          preparing: orders.filter(o => o.status === OrderStatus.PREPARING).length,
          ready: orders.filter(o => o.status === OrderStatus.READY).length,
          completed: orders.filter(o => o.status === OrderStatus.READY).length,
          cancelled: orders.filter(o => o.status === OrderStatus.CANCELLED).length,
          averagePrepTime: calculateAveragePrepTime(orders),
          overdueCount: orders.filter(o => o.isOverdue).length,
        }
        
        set({ stats })
      },

      getOrdersByStatus: (status: OrderStatus) => {
        const { orders } = get()
        return orders.filter(order => order.status === status)
      },

      getOrdersByStation: (stationId: string) => {
        const { orders } = get()
        return orders.filter(order => order.station?.id === stationId)
      },

      getOverdueOrders: () => {
        const { orders } = get()
        return orders.filter(order => order.isOverdue)
      },

      updateOrderTimes: () => {
        const { orders, settings } = get()
        
        const updatedOrders = orders.map(order => enhanceOrder(order, settings))
        
        // 檢查是否有新的超時訂單
        const newOverdueOrders = updatedOrders.filter(order => 
          order.isOverdue && !orders.find(o => o.id === order.id)?.isOverdue
        )
        
        if (newOverdueOrders.length > 0 && settings.enableSound && settings.overdueSound) {
          playSound('overdue')
        }
        
        set({ orders: updatedOrders })
      },
    }),
    {
      name: 'kds-storage',
      partialize: (state) => ({
        settings: state.settings,
        selectedStation: state.selectedStation,
      }),
    }
  )
)

// 輔助函數：增強訂單數據
function enhanceOrder(order: KDSOrder, settings: KDSSettings): KDSOrder {
  const orderTime = dayjs(order.orderTime)
  const now = dayjs()
  const elapsedMinutes = now.diff(orderTime, 'minute')
  
  const prepTime = order.orderItems.reduce((total, item) => {
    return total + (item.menuItem?.prepTime || settings.defaultPrepTime)
  }, 0)
  
  const remainingTime = Math.max(0, prepTime - elapsedMinutes)
  const isOverdue = elapsedMinutes > (prepTime + settings.overdueTime)
  
  let priority = OrderPriority.NORMAL
  if (isOverdue) {
    priority = OrderPriority.URGENT
  } else if (elapsedMinutes > prepTime - settings.warningTime) {
    priority = OrderPriority.HIGH
  }
  
  return {
    ...order,
    elapsedTime: elapsedMinutes,
    remainingTime,
    isOverdue,
    priority,
  }
}

// 輔助函數：計算平均製作時間
function calculateAveragePrepTime(orders: KDSOrder[]): number {
  const completedOrders = orders.filter(order => 
    order.status === OrderStatus.READY && order.completedAt
  )
  
  if (completedOrders.length === 0) return 0
  
  const totalTime = completedOrders.reduce((sum, order) => {
    const orderTime = dayjs(order.orderTime)
    const completedTime = dayjs(order.completedAt)
    return sum + completedTime.diff(orderTime, 'minute')
  }, 0)
  
  return Math.round(totalTime / completedOrders.length)
}

// 輔助函數：播放聲音
function playSound(type: 'new-order' | 'overdue') {
  // 在實際應用中，這裡會播放相應的聲音文件
  console.log(`Playing sound: ${type}`)
  
  // 簡單的瀏覽器提示音
  if ('AudioContext' in window) {
    const audioContext = new AudioContext()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()
    
    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)
    
    oscillator.frequency.value = type === 'new-order' ? 800 : 400
    oscillator.type = 'sine'
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5)
    
    oscillator.start(audioContext.currentTime)
    oscillator.stop(audioContext.currentTime + 0.5)
  }
}
