# 早餐店 POS 系統 - 前端

## 功能特色

- 🎯 觸控友善的點餐界面
- 🛒 即時購物車管理
- 💳 多種支付方式支援
- 📱 響應式設計，支援平板和桌面
- 🔄 即時訂單狀態同步
- 🖨️ 收據打印功能
- 👥 多用戶角色權限管理

## 技術棧

- **框架**: React 18 + TypeScript
- **構建工具**: Vite
- **UI 庫**: Ant Design
- **狀態管理**: Zustand
- **路由**: React Router
- **HTTP 客戶端**: Axios
- **即時通訊**: Socket.IO Client
- **表單處理**: React Hook Form + Zod
- **樣式**: CSS + Ant Design

## 快速開始

### 環境要求
- Node.js 18+
- npm 或 yarn

### 安裝依賴
```bash
npm install
```

### 環境配置
1. 複製環境變數文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的 API 地址

### 開發模式運行
```bash
npm run dev
```

應用將在 http://localhost:3000 啟動

### 生產構建
```bash
npm run build
```

## 項目結構

```
src/
├── components/          # 可重用組件
│   ├── auth/           # 認證相關組件
│   ├── layout/         # 佈局組件
│   ├── pos/            # POS 專用組件
│   └── common/         # 通用組件
├── pages/              # 頁面組件
│   ├── LoginPage.tsx   # 登入頁面
│   ├── POSPage.tsx     # POS 主頁面
│   ├── OrderHistoryPage.tsx # 訂單記錄
│   └── SettingsPage.tsx # 設定頁面
├── stores/             # Zustand 狀態管理
│   ├── authStore.ts    # 認證狀態
│   ├── cartStore.ts    # 購物車狀態
│   └── orderStore.ts   # 訂單狀態
├── services/           # API 服務
│   ├── api.ts          # API 基礎配置
│   ├── authService.ts  # 認證服務
│   ├── menuService.ts  # 菜單服務
│   └── orderService.ts # 訂單服務
├── hooks/              # 自定義 Hooks
├── types/              # TypeScript 類型定義
├── utils/              # 工具函數
└── styles/             # 樣式文件
```

## 主要功能

### 1. 用戶認證
- 支援電子郵件登入
- 演示帳號快速登入
- JWT Token 自動刷新
- 角色權限控制

### 2. POS 點餐
- 分類瀏覽菜品
- 觸控友善的菜品選擇
- 購物車即時更新
- 商品備註功能
- 庫存檢查

### 3. 訂單管理
- 訂單創建和修改
- 多種支付方式
- 訂單狀態追蹤
- 收據打印

### 4. 即時同步
- Socket.IO 即時通訊
- 訂單狀態即時更新
- 庫存變動通知
- 系統通知

## 演示帳號

系統提供以下演示帳號：

- **管理員**: <EMAIL> / admin123
- **收銀員**: <EMAIL> / cashier123
- **廚房**: <EMAIL> / kitchen123

## 開發指南

### 添加新頁面
1. 在 `src/pages/` 創建新的頁面組件
2. 在 `src/App.tsx` 中添加路由
3. 如需要，在側邊欄菜單中添加導航

### 添加新的 API 服務
1. 在 `src/services/` 創建服務文件
2. 使用 `src/services/api.ts` 中的請求方法
3. 定義相應的 TypeScript 類型

### 狀態管理
使用 Zustand 進行狀態管理：
```typescript
import { create } from 'zustand'

interface MyState {
  count: number
  increment: () => void
}

export const useMyStore = create<MyState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
}))
```

### 樣式指南
- 優先使用 Ant Design 組件
- 觸控友善設計（最小點擊區域 48px）
- 響應式設計支援
- 使用 CSS 類名前綴 `pos-`

## 部署

### Docker 部署
```bash
# 開發環境
docker build -f Dockerfile.dev -t pos-frontend:dev .
docker run -p 3000:3000 pos-frontend:dev

# 生產環境
docker build -f Dockerfile.prod -t pos-frontend:prod .
docker run -p 80:80 pos-frontend:prod
```

### 靜態部署
```bash
npm run build
# 將 dist/ 目錄部署到靜態服務器
```

## 測試

```bash
# 運行測試
npm run test

# 測試覆蓋率
npm run test:coverage
```

## 故障排除

### 常見問題

1. **API 連接失敗**
   - 檢查 `.env` 中的 API 地址
   - 確認後端服務正在運行

2. **Socket.IO 連接失敗**
   - 檢查 Socket.IO 服務器地址
   - 確認防火牆設置

3. **打印功能不工作**
   - 檢查打印機 IP 配置
   - 確認打印機網路連接

### 性能優化

- 使用 React.memo 優化組件渲染
- 實現虛擬滾動處理大量菜品
- 使用 React Query 緩存 API 請求
- 圖片懶加載和壓縮
