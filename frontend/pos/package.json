{"name": "breakfast-pos-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "zustand": "^4.4.7", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "dayjs": "^1.11.10", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "lodash": "^4.17.21", "qrcode": "^1.5.3", "html2canvas": "^1.4.1", "jspdf": "^2.5.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1"}}