// 用戶相關類型
export interface User {
  id: string
  email: string
  username: string
  name: string
  role: UserRole
  storeId?: string
  store?: Store
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  CASHIER = 'CASHIER',
  KITCHEN = 'K<PERSON>CH<PERSON>',
}

// 門店相關類型
export interface Store {
  id: string
  name: string
  address?: string
  phone?: string
  email?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 分類相關類型
export interface Category {
  id: string
  name: string
  description?: string
  sortOrder: number
  isActive: boolean
  storeId: string
  createdAt: string
  updatedAt: string
}

// 菜品相關類型
export interface MenuItem {
  id: string
  name: string
  description?: string
  price: number
  cost?: number
  image?: string
  isActive: boolean
  isAvailable: boolean
  categoryId: string
  category?: Category
  storeId: string
  stock?: number
  lowStockAlert?: number
  prepTime?: number
  createdAt: string
  updatedAt: string
}

// 訂單相關類型
export interface Order {
  id: string
  orderNumber: string
  status: OrderStatus
  customerName?: string
  customerPhone?: string
  subtotal: number
  tax: number
  discount: number
  total: number
  paymentMethod?: PaymentMethod
  paymentStatus: PaymentStatus
  paidAt?: string
  orderType: OrderType
  notes?: string
  orderTime: string
  estimatedTime?: string
  completedAt?: string
  storeId: string
  store?: Store
  userId: string
  user?: User
  orderItems: OrderItem[]
  createdAt: string
  updatedAt: string
}

export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
}

export enum PaymentMethod {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  MOBILE_PAYMENT = 'MOBILE_PAYMENT',
  BANK_TRANSFER = 'BANK_TRANSFER',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

export enum OrderType {
  DINE_IN = 'DINE_IN',
  TAKEAWAY = 'TAKEAWAY',
  DELIVERY = 'DELIVERY',
}

// 訂單項目類型
export interface OrderItem {
  id: string
  quantity: number
  unitPrice: number
  totalPrice: number
  notes?: string
  orderId: string
  menuItemId: string
  menuItem?: MenuItem
  createdAt: string
  updatedAt: string
}

// 購物車項目類型
export interface CartItem {
  menuItem: MenuItem
  quantity: number
  notes?: string
  subtotal: number
}

// 系統設定類型
export interface Setting {
  id: string
  key: string
  value: string
  createdAt: string
  updatedAt: string
}

// API 響應類型
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: any[]
}

// 分頁類型
export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 認證相關類型
export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  user: User
  token: string
  refreshToken: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
  name: string
  role?: UserRole
  storeId?: string
}

// 訂單創建請求類型
export interface CreateOrderRequest {
  orderItems: {
    menuItemId: string
    quantity: number
    notes?: string
  }[]
  customerName?: string
  customerPhone?: string
  orderType: OrderType
  notes?: string
}

// Socket.IO 事件類型
export interface SocketEvents {
  // 連接事件
  connection: void
  disconnect: void
  authenticate: { token: string }
  authenticated: { userId: string; userRole: string; storeId?: string }
  
  // 訂單事件
  order_created: Order
  order_updated: Order
  order_status_changed: { orderId: string; status: OrderStatus; userId: string }
  order_cancelled: { orderId: string; reason?: string }
  
  // KDS 事件
  kds_order_received: Order
  kds_order_started: Order
  kds_order_completed: Order
  
  // POS 事件
  pos_order_ready: Order
  pos_payment_completed: Order
  
  // 庫存事件
  inventory_low_stock: { itemId: string; itemName: string; currentStock: number; alertLevel: number }
  inventory_out_of_stock: { itemId: string; itemName: string }
  
  // 系統事件
  system_notification: { type: string; message: string; data?: any }
  store_status_changed: { storeId: string; isActive: boolean }
}

// 錯誤類型
export interface AppError {
  message: string
  code?: string
  statusCode?: number
  details?: any
}
