import { create } from 'zustand'
import { CartItem, MenuItem, OrderType } from '@/types'
import toast from 'react-hot-toast'

interface CartState {
  items: CartItem[]
  customerName: string
  customerPhone: string
  orderType: OrderType
  notes: string
  discount: number
  
  // Computed values
  subtotal: number
  tax: number
  total: number
  itemCount: number
  
  // Actions
  addItem: (menuItem: MenuItem, quantity?: number, notes?: string) => void
  removeItem: (menuItemId: string) => void
  updateItemQuantity: (menuItemId: string, quantity: number) => void
  updateItemNotes: (menuItemId: string, notes: string) => void
  clearCart: () => void
  setCustomerInfo: (name: string, phone: string) => void
  setOrderType: (type: OrderType) => void
  setNotes: (notes: string) => void
  setDiscount: (discount: number) => void
  calculateTotals: () => void
}

const TAX_RATE = 0.05 // 5% 稅率

export const useCartStore = create<CartState>((set, get) => ({
  items: [],
  customerName: '',
  customerPhone: '',
  orderType: OrderType.DINE_IN,
  notes: '',
  discount: 0,
  subtotal: 0,
  tax: 0,
  total: 0,
  itemCount: 0,

  addItem: (menuItem: MenuItem, quantity = 1, notes = '') => {
    const { items } = get()
    
    // 檢查庫存
    if (menuItem.stock !== undefined && menuItem.stock <= 0) {
      toast.error(`${menuItem.name} 已售完`)
      return
    }
    
    // 檢查是否可售
    if (!menuItem.isAvailable) {
      toast.error(`${menuItem.name} 暫時無法供應`)
      return
    }
    
    const existingItemIndex = items.findIndex(item => item.menuItem.id === menuItem.id)
    
    if (existingItemIndex >= 0) {
      // 更新現有項目
      const existingItem = items[existingItemIndex]
      const newQuantity = existingItem.quantity + quantity
      
      // 檢查庫存限制
      if (menuItem.stock !== undefined && newQuantity > menuItem.stock) {
        toast.error(`${menuItem.name} 庫存不足，目前庫存：${menuItem.stock}`)
        return
      }
      
      const updatedItems = [...items]
      updatedItems[existingItemIndex] = {
        ...existingItem,
        quantity: newQuantity,
        notes: notes || existingItem.notes,
        subtotal: newQuantity * menuItem.price,
      }
      
      set({ items: updatedItems })
    } else {
      // 添加新項目
      if (menuItem.stock !== undefined && quantity > menuItem.stock) {
        toast.error(`${menuItem.name} 庫存不足，目前庫存：${menuItem.stock}`)
        return
      }
      
      const newItem: CartItem = {
        menuItem,
        quantity,
        notes,
        subtotal: quantity * menuItem.price,
      }
      
      set({ items: [...items, newItem] })
    }
    
    get().calculateTotals()
    toast.success(`已添加 ${menuItem.name} 到購物車`)
  },

  removeItem: (menuItemId: string) => {
    const { items } = get()
    const item = items.find(item => item.menuItem.id === menuItemId)
    
    if (item) {
      const updatedItems = items.filter(item => item.menuItem.id !== menuItemId)
      set({ items: updatedItems })
      get().calculateTotals()
      toast.success(`已移除 ${item.menuItem.name}`)
    }
  },

  updateItemQuantity: (menuItemId: string, quantity: number) => {
    const { items } = get()
    
    if (quantity <= 0) {
      get().removeItem(menuItemId)
      return
    }
    
    const itemIndex = items.findIndex(item => item.menuItem.id === menuItemId)
    
    if (itemIndex >= 0) {
      const item = items[itemIndex]
      
      // 檢查庫存限制
      if (item.menuItem.stock !== undefined && quantity > item.menuItem.stock) {
        toast.error(`${item.menuItem.name} 庫存不足，目前庫存：${item.menuItem.stock}`)
        return
      }
      
      const updatedItems = [...items]
      updatedItems[itemIndex] = {
        ...item,
        quantity,
        subtotal: quantity * item.menuItem.price,
      }
      
      set({ items: updatedItems })
      get().calculateTotals()
    }
  },

  updateItemNotes: (menuItemId: string, notes: string) => {
    const { items } = get()
    const itemIndex = items.findIndex(item => item.menuItem.id === menuItemId)
    
    if (itemIndex >= 0) {
      const updatedItems = [...items]
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        notes,
      }
      
      set({ items: updatedItems })
    }
  },

  clearCart: () => {
    set({
      items: [],
      customerName: '',
      customerPhone: '',
      orderType: OrderType.DINE_IN,
      notes: '',
      discount: 0,
      subtotal: 0,
      tax: 0,
      total: 0,
      itemCount: 0,
    })
  },

  setCustomerInfo: (name: string, phone: string) => {
    set({ customerName: name, customerPhone: phone })
  },

  setOrderType: (type: OrderType) => {
    set({ orderType: type })
  },

  setNotes: (notes: string) => {
    set({ notes })
  },

  setDiscount: (discount: number) => {
    set({ discount: Math.max(0, discount) })
    get().calculateTotals()
  },

  calculateTotals: () => {
    const { items, discount } = get()
    
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0)
    const discountAmount = Math.min(discount, subtotal)
    const taxableAmount = subtotal - discountAmount
    const tax = taxableAmount * TAX_RATE
    const total = taxableAmount + tax
    const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)
    
    set({
      subtotal,
      tax,
      total,
      itemCount,
    })
  },
}))

// 自動計算總計
useCartStore.subscribe((state) => {
  state.calculateTotals()
})
