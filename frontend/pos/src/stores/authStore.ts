import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, LoginRequest, LoginResponse } from '@/types'
import { authService } from '@/services/authService'
import toast from 'react-hot-toast'

interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (credentials: LoginRequest) => Promise<boolean>
  logout: () => void
  refreshAuth: () => Promise<boolean>
  initializeAuth: () => void
  updateUser: (user: User) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials: LoginRequest) => {
        set({ isLoading: true })
        
        try {
          const response = await authService.login(credentials)
          
          if (response.success && response.data) {
            const { user, token, refreshToken } = response.data as LoginResponse
            
            set({
              user,
              token,
              refreshToken,
              isAuthenticated: true,
              isLoading: false,
            })
            
            toast.success(`歡迎回來，${user.name}！`)
            return true
          } else {
            throw new Error(response.message || '登入失敗')
          }
        } catch (error: any) {
          set({ isLoading: false })
          toast.error(error.message || '登入失敗，請檢查您的帳號密碼')
          return false
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
        })
        
        // 清除本地存儲
        localStorage.removeItem('auth-storage')
        
        toast.success('已成功登出')
      },

      refreshAuth: async () => {
        const { refreshToken } = get()
        
        if (!refreshToken) {
          return false
        }

        try {
          const response = await authService.refreshToken(refreshToken)
          
          if (response.success && response.data) {
            const { token, refreshToken: newRefreshToken } = response.data
            
            set({
              token,
              refreshToken: newRefreshToken,
            })
            
            return true
          } else {
            throw new Error('Token 刷新失敗')
          }
        } catch (error) {
          // Token 刷新失敗，清除認證狀態
          get().logout()
          return false
        }
      },

      initializeAuth: () => {
        const { token, user } = get()
        
        if (token && user) {
          set({ isAuthenticated: true })
          
          // 驗證 token 是否仍然有效
          authService.getCurrentUser()
            .then((response) => {
              if (response.success && response.data) {
                set({ user: response.data.user })
              } else {
                get().logout()
              }
            })
            .catch(() => {
              get().logout()
            })
        }
      },

      updateUser: (user: User) => {
        set({ user })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
