import { useState } from 'react'
import { 
  Card, 
  Typography, 
  Space, 
  Button, 
  Input, 
  Select, 
  Divider, 
  Modal, 
  Form,
  Radio,
  message,
} from 'antd'
import { 
  PlusOutlined, 
  MinusOutlined, 
  DeleteOutlined, 
  ShoppingCartOutlined,
  CreditCardOutlined,
  DollarOutlined,
  MobileOutlined,
  BankOutlined,
} from '@ant-design/icons'
import { useCartStore } from '@/stores/cartStore'
import { orderService } from '@/services/orderService'
import { socketService } from '@/services/socketService'
import { OrderType, PaymentMethod } from '@/types'
import toast from 'react-hot-toast'

const { Title, Text } = Typography
const { TextArea } = Input

const CartPanel = () => {
  const {
    items,
    customerName,
    customerPhone,
    orderType,
    notes,
    subtotal,
    tax,
    total,
    itemCount,
    updateItemQuantity,
    removeItem,
    updateItemNotes,
    setCustomerInfo,
    setOrderType,
    setNotes,
    clearCart,
  } = useCartStore()

  const [checkoutModalVisible, setCheckoutModalVisible] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.CASH)
  const [isProcessing, setIsProcessing] = useState(false)
  const [form] = Form.useForm()

  const handleQuantityChange = (menuItemId: string, change: number) => {
    const item = items.find(item => item.menuItem.id === menuItemId)
    if (item) {
      const newQuantity = item.quantity + change
      if (newQuantity <= 0) {
        removeItem(menuItemId)
      } else {
        updateItemQuantity(menuItemId, newQuantity)
      }
    }
  }

  const handleNotesChange = (menuItemId: string, notes: string) => {
    updateItemNotes(menuItemId, notes)
  }

  const handleCheckout = () => {
    if (items.length === 0) {
      message.warning('購物車是空的')
      return
    }
    
    form.setFieldsValue({
      customerName,
      customerPhone,
      orderType,
      notes,
      paymentMethod,
    })
    
    setCheckoutModalVisible(true)
  }

  const handleConfirmOrder = async () => {
    try {
      const values = await form.validateFields()
      setIsProcessing(true)

      // 更新購物車信息
      setCustomerInfo(values.customerName || '', values.customerPhone || '')
      setOrderType(values.orderType)
      setNotes(values.notes || '')

      // 創建訂單
      const orderData = {
        orderItems: items.map(item => ({
          menuItemId: item.menuItem.id,
          quantity: item.quantity,
          notes: item.notes,
        })),
        customerName: values.customerName,
        customerPhone: values.customerPhone,
        orderType: values.orderType,
        notes: values.notes,
      }

      const response = await orderService.createOrder(orderData)

      if (response.success && response.data) {
        const order = response.data.order
        
        // 通過 Socket.IO 廣播新訂單
        socketService.createOrder(order)
        
        toast.success(`訂單 ${order.orderNumber} 創建成功！`)
        
        // 清空購物車
        clearCart()
        setCheckoutModalVisible(false)
        
        // TODO: 可以選擇打印收據或顯示訂單詳情
      } else {
        throw new Error(response.message || '創建訂單失敗')
      }
    } catch (error: any) {
      console.error('創建訂單失敗:', error)
      toast.error(error.message || '創建訂單失敗')
    } finally {
      setIsProcessing(false)
    }
  }

  const getPaymentIcon = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return <DollarOutlined />
      case PaymentMethod.CREDIT_CARD:
        return <CreditCardOutlined />
      case PaymentMethod.MOBILE_PAYMENT:
        return <MobileOutlined />
      case PaymentMethod.BANK_TRANSFER:
        return <BankOutlined />
      default:
        return <DollarOutlined />
    }
  }

  const getPaymentText = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return '現金'
      case PaymentMethod.CREDIT_CARD:
        return '信用卡'
      case PaymentMethod.MOBILE_PAYMENT:
        return '行動支付'
      case PaymentMethod.BANK_TRANSFER:
        return '銀行轉帳'
      default:
        return '現金'
    }
  }

  return (
    <>
      <div className="pos-cart">
        <div className="pos-cart-header">
          <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: 8 }}>
            <ShoppingCartOutlined />
            購物車 ({itemCount})
          </Title>
        </div>

        <div className="pos-cart-items">
          {items.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px 20px', color: '#999' }}>
              <ShoppingCartOutlined style={{ fontSize: 48, marginBottom: 16 }} />
              <p>購物車是空的</p>
              <p style={{ fontSize: 12 }}>點擊菜品添加到購物車</p>
            </div>
          ) : (
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {items.map(item => (
                <Card key={item.menuItem.id} size="small" style={{ width: '100%' }}>
                  <div className="pos-cart-item">
                    <div style={{ flex: 1 }}>
                      <Text strong>{item.menuItem.name}</Text>
                      <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                        NT$ {item.menuItem.price} × {item.quantity} = NT$ {item.subtotal}
                      </div>
                      
                      {/* 商品備註 */}
                      <Input.TextArea
                        placeholder="備註（可選）"
                        value={item.notes}
                        onChange={(e) => handleNotesChange(item.menuItem.id, e.target.value)}
                        rows={2}
                        style={{ marginTop: 8, fontSize: 12 }}
                      />
                    </div>
                    
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 8, alignItems: 'center' }}>
                      {/* 數量控制 */}
                      <div className="pos-quantity-controls">
                        <Button
                          className="pos-quantity-btn"
                          size="small"
                          icon={<MinusOutlined />}
                          onClick={() => handleQuantityChange(item.menuItem.id, -1)}
                        />
                        <span style={{ minWidth: 30, textAlign: 'center', fontWeight: 'bold' }}>
                          {item.quantity}
                        </span>
                        <Button
                          className="pos-quantity-btn"
                          size="small"
                          icon={<PlusOutlined />}
                          onClick={() => handleQuantityChange(item.menuItem.id, 1)}
                        />
                      </div>
                      
                      {/* 刪除按鈕 */}
                      <Button
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => removeItem(item.menuItem.id)}
                      />
                    </div>
                  </div>
                </Card>
              ))}
            </Space>
          )}
        </div>

        {items.length > 0 && (
          <div className="pos-cart-footer">
            {/* 總計區域 */}
            <div className="pos-total-section">
              <div className="pos-total-row">
                <Text>小計</Text>
                <Text>NT$ {subtotal.toFixed(0)}</Text>
              </div>
              <div className="pos-total-row">
                <Text>稅額 (5%)</Text>
                <Text>NT$ {tax.toFixed(0)}</Text>
              </div>
              <div className="pos-total-row">
                <Text strong>總計</Text>
                <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
                  NT$ {total.toFixed(0)}
                </Text>
              </div>
            </div>

            {/* 結帳按鈕 */}
            <Button
              type="primary"
              size="large"
              block
              onClick={handleCheckout}
              style={{ height: 56, fontSize: 16, fontWeight: 'bold' }}
            >
              結帳
            </Button>
          </div>
        )}
      </div>

      {/* 結帳對話框 */}
      <Modal
        title="確認訂單"
        open={checkoutModalVisible}
        onOk={handleConfirmOrder}
        onCancel={() => setCheckoutModalVisible(false)}
        okText="確認下單"
        cancelText="取消"
        width={600}
        confirmLoading={isProcessing}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="customerName"
            label="客戶姓名"
          >
            <Input placeholder="請輸入客戶姓名（可選）" />
          </Form.Item>

          <Form.Item
            name="customerPhone"
            label="聯絡電話"
          >
            <Input placeholder="請輸入聯絡電話（可選）" />
          </Form.Item>

          <Form.Item
            name="orderType"
            label="訂單類型"
            rules={[{ required: true, message: '請選擇訂單類型' }]}
          >
            <Radio.Group>
              <Radio.Button value={OrderType.DINE_IN}>內用</Radio.Button>
              <Radio.Button value={OrderType.TAKEAWAY}>外帶</Radio.Button>
              <Radio.Button value={OrderType.DELIVERY}>外送</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="paymentMethod"
            label="支付方式"
            rules={[{ required: true, message: '請選擇支付方式' }]}
          >
            <Select
              value={paymentMethod}
              onChange={setPaymentMethod}
              style={{ width: '100%' }}
            >
              <Select.Option value={PaymentMethod.CASH}>
                <Space>
                  {getPaymentIcon(PaymentMethod.CASH)}
                  {getPaymentText(PaymentMethod.CASH)}
                </Space>
              </Select.Option>
              <Select.Option value={PaymentMethod.CREDIT_CARD}>
                <Space>
                  {getPaymentIcon(PaymentMethod.CREDIT_CARD)}
                  {getPaymentText(PaymentMethod.CREDIT_CARD)}
                </Space>
              </Select.Option>
              <Select.Option value={PaymentMethod.MOBILE_PAYMENT}>
                <Space>
                  {getPaymentIcon(PaymentMethod.MOBILE_PAYMENT)}
                  {getPaymentText(PaymentMethod.MOBILE_PAYMENT)}
                </Space>
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="訂單備註"
          >
            <TextArea rows={3} placeholder="特殊要求或備註（可選）" />
          </Form.Item>
        </Form>

        <Divider />

        {/* 訂單摘要 */}
        <div>
          <Title level={5}>訂單摘要</Title>
          <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 8 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text>小計</Text>
              <Text>NT$ {subtotal.toFixed(0)}</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text>稅額</Text>
              <Text>NT$ {tax.toFixed(0)}</Text>
            </div>
            <Divider style={{ margin: '8px 0' }} />
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text strong>總計</Text>
              <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
                NT$ {total.toFixed(0)}
              </Text>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default CartPanel
