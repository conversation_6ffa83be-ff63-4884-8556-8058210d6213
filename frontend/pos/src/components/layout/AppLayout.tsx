import { ReactNode, useState } from 'react'
import { Layout, Menu, Button, Avatar, Dropdown, Typography, Space } from 'antd'
import { 
  MenuOutlined, 
  ShoppingCartOutlined, 
  HistoryOutlined, 
  SettingOutlined,
  LogoutOutlined,
  UserOutlined,
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'
import { useCartStore } from '@/stores/cartStore'

const { Header, Content, Sider } = Layout
const { Text } = Typography

interface AppLayoutProps {
  children: ReactNode
}

const AppLayout = ({ children }: AppLayoutProps) => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuthStore()
  const { itemCount } = useCartStore()

  const menuItems = [
    {
      key: '/',
      icon: <ShoppingCartOutlined />,
      label: 'POS 點餐',
    },
    {
      key: '/orders',
      icon: <HistoryOutlined />,
      label: '訂單記錄',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系統設定',
    },
  ]

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '個人資料',
      onClick: () => {
        // TODO: 實現個人資料頁面
      },
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '登出',
      onClick: logout,
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        width={240}
        style={{
          background: '#fff',
          borderRight: '1px solid #f0f0f0',
        }}
      >
        <div style={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start',
          padding: collapsed ? 0 : '0 24px',
          borderBottom: '1px solid #f0f0f0',
        }}>
          {!collapsed && (
            <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
              早餐店 POS
            </Text>
          )}
          {collapsed && (
            <ShoppingCartOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          )}
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none', marginTop: 16 }}
        />
      </Sider>
      
      <Layout>
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: 16 }}
          />
          
          <Space size="large">
            {/* 購物車項目數量 */}
            {location.pathname === '/' && itemCount > 0 && (
              <Space>
                <ShoppingCartOutlined />
                <Text>{itemCount} 項商品</Text>
              </Space>
            )}
            
            {/* 門店信息 */}
            {user?.store && (
              <Text type="secondary">{user.store.name}</Text>
            )}
            
            {/* 用戶菜單 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Button type="text" style={{ height: 'auto', padding: '4px 8px' }}>
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  <Text>{user?.name}</Text>
                </Space>
              </Button>
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{
          margin: 0,
          padding: 0,
          background: '#f5f5f5',
          overflow: 'hidden',
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}

export default AppLayout
