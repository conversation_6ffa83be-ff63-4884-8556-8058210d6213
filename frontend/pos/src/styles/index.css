/* 重置樣式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 觸控友善樣式 */
.touch-friendly {
  min-height: 48px;
  min-width: 48px;
  padding: 12px;
  font-size: 16px;
}

/* POS 專用樣式 */
.pos-container {
  height: 100vh;
  overflow: hidden;
}

.pos-menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.pos-menu-item {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px solid #d9d9d9;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  user-select: none;
}

.pos-menu-item:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.pos-menu-item:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.pos-menu-item.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.pos-menu-item.unavailable:hover {
  border-color: #d9d9d9;
  box-shadow: none;
  transform: none;
}

.pos-cart {
  background: white;
  border-left: 1px solid #f0f0f0;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.pos-cart-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.pos-cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.pos-cart-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.pos-cart-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.pos-cart-item:last-child {
  border-bottom: none;
}

.pos-quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pos-quantity-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  background: white;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.pos-quantity-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.pos-quantity-btn:active {
  transform: scale(0.95);
}

.pos-total-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.pos-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.pos-total-row:last-child {
  margin-bottom: 0;
  font-weight: bold;
  font-size: 18px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.pos-payment-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.pos-payment-btn {
  height: 56px;
  font-size: 16px;
  font-weight: 500;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .pos-menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
    padding: 12px;
  }
  
  .pos-menu-item {
    padding: 12px;
  }
  
  .pos-payment-buttons {
    grid-template-columns: 1fr;
  }
}

/* 打印樣式 */
@media print {
  body * {
    visibility: hidden;
  }
  
  .print-area, .print-area * {
    visibility: visible;
  }
  
  .print-area {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
}

/* 載入動畫 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
