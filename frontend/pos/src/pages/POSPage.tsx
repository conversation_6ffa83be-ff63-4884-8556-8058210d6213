import { useState, useEffect } from 'react'
import { Row, Col, Card, Typography, Spin, Alert } from 'antd'
import { MenuItem, Category } from '@/types'
import { useCartStore } from '@/stores/cartStore'

// 組件導入
import CartPanel from '@/components/pos/CartPanel'

const { Title } = Typography

const POSPage = () => {
  const [categories, setCategories] = useState<Category[]>([])
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')

  const { addItem } = useCartStore()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      // TODO: 實現 API 調用
      // const [categoriesRes, menuItemsRes] = await Promise.all([
      //   categoryService.getCategories(),
      //   menuItemService.getMenuItems()
      // ])
      
      // 模擬數據
      const mockCategories: Category[] = [
        {
          id: '1',
          name: '主餐',
          description: '漢堡、三明治等主要餐點',
          sortOrder: 1,
          isActive: true,
          storeId: 'store-1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: '飲品',
          description: '咖啡、茶類、果汁等飲品',
          sortOrder: 2,
          isActive: true,
          storeId: 'store-1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      const mockMenuItems: MenuItem[] = [
        {
          id: '1',
          name: '經典漢堡',
          description: '牛肉漢堡配生菜、番茄、起司',
          price: 120,
          cost: 60,
          isActive: true,
          isAvailable: true,
          categoryId: '1',
          storeId: 'store-1',
          stock: 50,
          lowStockAlert: 5,
          prepTime: 8,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: '美式咖啡',
          description: '香濃美式咖啡',
          price: 50,
          cost: 15,
          isActive: true,
          isAvailable: true,
          categoryId: '2',
          storeId: 'store-1',
          stock: 100,
          lowStockAlert: 10,
          prepTime: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      setCategories(mockCategories)
      setMenuItems(mockMenuItems)
      
      if (mockCategories.length > 0) {
        setSelectedCategory(mockCategories[0].id)
      }
    } catch (err: any) {
      setError(err.message || '載入數據失敗')
    } finally {
      setLoading(false)
    }
  }

  const filteredMenuItems = selectedCategory
    ? menuItems.filter(item => item.categoryId === selectedCategory)
    : menuItems

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: 24 }}>
        <Alert
          message="載入失敗"
          description={error}
          type="error"
          showIcon
        />
      </div>
    )
  }

  return (
    <div className="pos-container">
      <Row style={{ height: '100%' }}>
        {/* 菜單區域 */}
        <Col xs={24} lg={16} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <div style={{ padding: '16px 16px 0 16px' }}>
            <Title level={4} style={{ margin: 0 }}>菜單</Title>
          </div>
          
          {/* 分類標籤 - 暫時使用簡單實現 */}
          <div style={{ padding: '0 16px 16px 16px' }}>
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                style={{
                  marginRight: 8,
                  padding: '8px 16px',
                  border: selectedCategory === category.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  borderRadius: 6,
                  background: selectedCategory === category.id ? '#e6f7ff' : 'white',
                  cursor: 'pointer',
                }}
              >
                {category.name}
              </button>
            ))}
          </div>
          
          {/* 菜品網格 - 暫時使用簡單實現 */}
          <div className="pos-menu-grid" style={{ flex: 1 }}>
            {filteredMenuItems.map(item => (
              <Card
                key={item.id}
                className="pos-menu-item"
                hoverable
                onClick={() => addItem(item)}
                style={{
                  opacity: item.isAvailable ? 1 : 0.5,
                  cursor: item.isAvailable ? 'pointer' : 'not-allowed',
                }}
              >
                <div style={{ textAlign: 'center' }}>
                  <Title level={5} style={{ margin: '0 0 8px 0' }}>
                    {item.name}
                  </Title>
                  <p style={{ margin: '0 0 8px 0', fontSize: 12, color: '#666' }}>
                    {item.description}
                  </p>
                  <p style={{ margin: 0, fontSize: 16, fontWeight: 'bold', color: '#1890ff' }}>
                    NT$ {item.price}
                  </p>
                  {item.stock !== undefined && (
                    <p style={{ margin: '4px 0 0 0', fontSize: 12, color: '#999' }}>
                      庫存: {item.stock}
                    </p>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </Col>
        
        {/* 購物車區域 */}
        <Col xs={24} lg={8} style={{ height: '100%' }}>
          <CartPanel />
        </Col>
      </Row>
    </div>
  )
}

export default POSPage
