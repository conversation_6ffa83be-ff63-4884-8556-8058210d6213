import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import { useAuthStore } from '@/stores/authStore'
import { useSocket } from '@/services/socketService'
import { useEffect } from 'react'

// 頁面組件
import LoginPage from '@/pages/LoginPage'
import POSPage from '@/pages/POSPage'
import OrderHistoryPage from '@/pages/OrderHistoryPage'
import SettingsPage from '@/pages/SettingsPage'

// 佈局組件
import AppLayout from '@/components/layout/AppLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

function App() {
  const { isAuthenticated, initializeAuth } = useAuthStore()
  const { connect, disconnect } = useSocket()

  useEffect(() => {
    initializeAuth()
  }, [initializeAuth])

  useEffect(() => {
    if (isAuthenticated) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [isAuthenticated, connect, disconnect])

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Routes>
        {/* 登入頁面 */}
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
          }
        />

        {/* 受保護的路由 */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <AppLayout>
                <Routes>
                  <Route path="/" element={<POSPage />} />
                  <Route path="/orders" element={<OrderHistoryPage />} />
                  <Route path="/settings" element={<SettingsPage />} />
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </AppLayout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </Layout>
  )
}

export default App
