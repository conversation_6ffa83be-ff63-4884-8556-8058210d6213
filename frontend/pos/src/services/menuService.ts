import { get, post, put, del } from './api'
import { MenuItem, Category, ApiResponse, PaginationParams, PaginatedResponse } from '@/types'

export const menuService = {
  // 獲取菜品列表
  getMenuItems: (params?: PaginationParams & {
    categoryId?: string
    isActive?: boolean
    isAvailable?: boolean
  }): Promise<ApiResponse<PaginatedResponse<MenuItem>>> => {
    return get('/menu-items', { params })
  },

  // 獲取單個菜品
  getMenuItem: (id: string): Promise<ApiResponse<{ menuItem: MenuItem }>> => {
    return get(`/menu-items/${id}`)
  },

  // 創建菜品
  createMenuItem: (data: {
    name: string
    description?: string
    price: number
    cost?: number
    categoryId: string
    stock?: number
    lowStockAlert?: number
    prepTime?: number
    image?: string
  }): Promise<ApiResponse<{ menuItem: MenuItem }>> => {
    return post('/menu-items', data)
  },

  // 更新菜品
  updateMenuItem: (id: string, data: Partial<MenuItem>): Promise<ApiResponse<{ menuItem: MenuItem }>> => {
    return put(`/menu-items/${id}`, data)
  },

  // 更新庫存
  updateStock: (id: string, data: {
    stock: number
    operation?: 'set' | 'add' | 'subtract'
  }): Promise<ApiResponse<{ menuItem: MenuItem }>> => {
    return put(`/menu-items/${id}/stock`, data)
  },

  // 刪除菜品
  deleteMenuItem: (id: string): Promise<ApiResponse> => {
    return del(`/menu-items/${id}`)
  },

  // 獲取分類列表
  getCategories: (params?: {
    isActive?: boolean
  }): Promise<ApiResponse<{ categories: Category[] }>> => {
    return get('/categories', { params })
  },

  // 獲取單個分類
  getCategory: (id: string): Promise<ApiResponse<{ category: Category }>> => {
    return get(`/categories/${id}`)
  },

  // 創建分類
  createCategory: (data: {
    name: string
    description?: string
    sortOrder?: number
  }): Promise<ApiResponse<{ category: Category }>> => {
    return post('/categories', data)
  },

  // 更新分類
  updateCategory: (id: string, data: Partial<Category>): Promise<ApiResponse<{ category: Category }>> => {
    return put(`/categories/${id}`, data)
  },

  // 刪除分類
  deleteCategory: (id: string): Promise<ApiResponse> => {
    return del(`/categories/${id}`)
  },
}
