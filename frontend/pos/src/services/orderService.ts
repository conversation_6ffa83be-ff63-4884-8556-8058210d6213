import { get, post, put, del } from './api'
import { Order, OrderStatus, CreateOrderRequest, ApiResponse, PaginationParams, PaginatedResponse } from '@/types'

export const orderService = {
  // 獲取訂單列表
  getOrders: (params?: PaginationParams & {
    status?: OrderStatus[]
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<Order>>> => {
    return get('/orders', { params })
  },

  // 獲取單個訂單
  getOrder: (id: string): Promise<ApiResponse<{ order: Order }>> => {
    return get(`/orders/${id}`)
  },

  // 創建訂單
  createOrder: (data: CreateOrderRequest): Promise<ApiResponse<{ order: Order }>> => {
    return post('/orders', data)
  },

  // 更新訂單狀態
  updateOrderStatus: (id: string, data: {
    status: OrderStatus
    notes?: string
  }): Promise<ApiResponse<{ order: Order }>> => {
    return put(`/orders/${id}/status`, data)
  },

  // 取消訂單
  cancelOrder: (id: string, reason?: string): Promise<ApiResponse<{ order: Order }>> => {
    return del(`/orders/${id}`, { data: { reason } })
  },

  // 獲取廚房訂單（用於 KDS）
  getKitchenOrders: (): Promise<ApiResponse<{ orders: Order[] }>> => {
    return get('/orders/kitchen')
  },
}
