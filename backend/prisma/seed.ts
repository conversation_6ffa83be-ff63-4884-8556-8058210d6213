import { PrismaClient, UserRole, OrderStatus, PaymentMethod, PaymentStatus, OrderType } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('開始資料庫種子數據初始化...')

  // 創建預設門店
  const defaultStore = await prisma.store.upsert({
    where: { id: 'default-store' },
    update: {},
    create: {
      id: 'default-store',
      name: '美味早餐店',
      address: '台北市中正區中山南路1號',
      phone: '02-1234-5678',
      email: '<EMAIL>',
    },
  })

  console.log('✅ 預設門店創建完成')

  // 創建管理員用戶
  const hashedPassword = await bcrypt.hash('admin123', 10)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      name: '系統管理員',
      role: UserRole.ADMIN,
      storeId: defaultStore.id,
    },
  })

  // 創建收銀員用戶
  const cashierPassword = await bcrypt.hash('cashier123', 10)
  
  const cashierUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'cashier',
      password: cashierPassword,
      name: '收銀員',
      role: UserRole.CASHIER,
      storeId: defaultStore.id,
    },
  })

  // 創建廚房用戶
  const kitchenPassword = await bcrypt.hash('kitchen123', 10)
  
  const kitchenUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'kitchen',
      password: kitchenPassword,
      name: '廚房人員',
      role: UserRole.KITCHEN,
      storeId: defaultStore.id,
    },
  })

  console.log('✅ 預設用戶創建完成')

  // 創建菜品分類
  const categories = [
    { name: '主餐', description: '漢堡、三明治等主要餐點' },
    { name: '飲品', description: '咖啡、茶類、果汁等飲品' },
    { name: '配菜', description: '薯條、沙拉等配菜' },
    { name: '甜點', description: '蛋糕、餅乾等甜點' },
  ]

  const createdCategories = []
  for (let i = 0; i < categories.length; i++) {
    const category = await prisma.category.upsert({
      where: { 
        id: `category-${i + 1}` 
      },
      update: {},
      create: {
        id: `category-${i + 1}`,
        name: categories[i].name,
        description: categories[i].description,
        sortOrder: i + 1,
        storeId: defaultStore.id,
      },
    })
    createdCategories.push(category)
  }

  console.log('✅ 菜品分類創建完成')

  // 創建菜品
  const menuItems = [
    // 主餐
    {
      name: '經典漢堡',
      description: '牛肉漢堡配生菜、番茄、起司',
      price: 120,
      cost: 60,
      categoryId: createdCategories[0].id,
      stock: 50,
      prepTime: 8,
    },
    {
      name: '火腿三明治',
      description: '新鮮火腿配生菜、番茄',
      price: 80,
      cost: 40,
      categoryId: createdCategories[0].id,
      stock: 30,
      prepTime: 5,
    },
    {
      name: '培根蛋餅',
      description: '香脆培根配嫩蛋',
      price: 60,
      cost: 30,
      categoryId: createdCategories[0].id,
      stock: 40,
      prepTime: 6,
    },
    // 飲品
    {
      name: '美式咖啡',
      description: '香濃美式咖啡',
      price: 50,
      cost: 15,
      categoryId: createdCategories[1].id,
      stock: 100,
      prepTime: 3,
    },
    {
      name: '拿鐵咖啡',
      description: '香濃拿鐵咖啡',
      price: 70,
      cost: 25,
      categoryId: createdCategories[1].id,
      stock: 100,
      prepTime: 4,
    },
    {
      name: '柳橙汁',
      description: '新鮮現榨柳橙汁',
      price: 60,
      cost: 20,
      categoryId: createdCategories[1].id,
      stock: 50,
      prepTime: 2,
    },
    // 配菜
    {
      name: '薯條',
      description: '金黃酥脆薯條',
      price: 40,
      cost: 15,
      categoryId: createdCategories[2].id,
      stock: 60,
      prepTime: 5,
    },
    {
      name: '沙拉',
      description: '新鮮蔬菜沙拉',
      price: 50,
      cost: 20,
      categoryId: createdCategories[2].id,
      stock: 30,
      prepTime: 3,
    },
  ]

  for (let i = 0; i < menuItems.length; i++) {
    await prisma.menuItem.upsert({
      where: { id: `menu-item-${i + 1}` },
      update: {},
      create: {
        id: `menu-item-${i + 1}`,
        ...menuItems[i],
        storeId: defaultStore.id,
      },
    })
  }

  console.log('✅ 菜品創建完成')

  // 創建系統設定
  const settings = [
    { key: 'store_name', value: '美味早餐店' },
    { key: 'tax_rate', value: '0.05' },
    { key: 'currency', value: 'TWD' },
    { key: 'receipt_footer', value: '謝謝光臨，歡迎再次蒞臨！' },
    { key: 'order_timeout', value: '30' }, // 分鐘
  ]

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    })
  }

  console.log('✅ 系統設定創建完成')

  console.log('🎉 資料庫種子數據初始化完成！')
  console.log('預設帳號：')
  console.log('管理員 - <EMAIL> / admin123')
  console.log('收銀員 - <EMAIL> / cashier123')
  console.log('廚房 - <EMAIL> / kitchen123')
}

main()
  .catch((e) => {
    console.error('❌ 種子數據初始化失敗：', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
