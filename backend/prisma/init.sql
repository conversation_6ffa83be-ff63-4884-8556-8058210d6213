-- 初始化資料庫腳本
-- 創建資料庫 (如果不存在)
-- CREATE DATABASE breakfast_pos_dev;

-- 設置時區
SET timezone = 'Asia/Taipei';

-- 創建擴展 (如果需要)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 創建索引函數 (用於全文搜索)
CREATE OR REPLACE FUNCTION immutable_unaccent(text)
RETURNS text AS
$func$
SELECT unaccent('unaccent', $1)
$func$ LANGUAGE sql IMMUTABLE;

-- 創建自定義函數：生成訂單編號
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS text AS $$
DECLARE
    order_date text;
    sequence_num text;
BEGIN
    -- 獲取當前日期 (YYYYMMDD 格式)
    order_date := to_char(CURRENT_DATE, 'YYYYMMDD');
    
    -- 獲取當日訂單序號 (4位數，從0001開始)
    SELECT LPAD((COUNT(*) + 1)::text, 4, '0') INTO sequence_num
    FROM orders 
    WHERE DATE(order_time) = CURRENT_DATE;
    
    -- 返回格式：ORD + YYYYMMDD + 序號
    RETURN 'ORD' || order_date || sequence_num;
END;
$$ LANGUAGE plpgsql;

-- 創建觸發器函數：自動生成訂單編號
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 創建觸發器函數：更新庫存
CREATE OR REPLACE FUNCTION update_menu_item_stock()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- 新增訂單項目時減少庫存
        UPDATE menu_items 
        SET stock = stock - NEW.quantity
        WHERE id = NEW.menu_item_id AND stock >= NEW.quantity;
        
        -- 檢查庫存是否足夠
        IF NOT FOUND THEN
            RAISE EXCEPTION '庫存不足，無法完成訂單';
        END IF;
        
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- 更新訂單項目時調整庫存
        UPDATE menu_items 
        SET stock = stock + OLD.quantity - NEW.quantity
        WHERE id = NEW.menu_item_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- 刪除訂單項目時恢復庫存
        UPDATE menu_items 
        SET stock = stock + OLD.quantity
        WHERE id = OLD.menu_item_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 創建觸發器函數：記錄庫存變動
CREATE OR REPLACE FUNCTION log_inventory_change()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND OLD.stock != NEW.stock THEN
        INSERT INTO inventory_logs (type, quantity, reason, menu_item_id, user_id, created_at)
        VALUES (
            CASE 
                WHEN NEW.stock > OLD.stock THEN 'IN'::inventory_type
                ELSE 'OUT'::inventory_type
            END,
            ABS(NEW.stock - OLD.stock),
            '系統自動調整',
            NEW.id,
            'system', -- 系統操作
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 創建視圖：訂單統計
CREATE OR REPLACE VIEW order_statistics AS
SELECT 
    DATE(order_time) as order_date,
    store_id,
    COUNT(*) as total_orders,
    SUM(total) as total_revenue,
    AVG(total) as average_order_value,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_orders,
    COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_orders
FROM orders
GROUP BY DATE(order_time), store_id;

-- 創建視圖：熱門商品
CREATE OR REPLACE VIEW popular_items AS
SELECT 
    mi.id,
    mi.name,
    mi.category_id,
    c.name as category_name,
    SUM(oi.quantity) as total_sold,
    SUM(oi.total_price) as total_revenue,
    COUNT(DISTINCT oi.order_id) as order_count
FROM menu_items mi
JOIN order_items oi ON mi.id = oi.menu_item_id
JOIN orders o ON oi.order_id = o.id
JOIN categories c ON mi.category_id = c.id
WHERE o.status != 'CANCELLED'
GROUP BY mi.id, mi.name, mi.category_id, c.name
ORDER BY total_sold DESC;

-- 創建視圖：低庫存警告
CREATE OR REPLACE VIEW low_stock_items AS
SELECT 
    mi.id,
    mi.name,
    mi.stock,
    mi.low_stock_alert,
    c.name as category_name,
    s.name as store_name
FROM menu_items mi
JOIN categories c ON mi.category_id = c.id
JOIN stores s ON mi.store_id = s.id
WHERE mi.stock <= mi.low_stock_alert
AND mi.is_active = true;

-- 設置預設值
INSERT INTO settings (key, value) VALUES 
('system_initialized', 'true'),
('default_tax_rate', '0.05'),
('default_currency', 'TWD'),
('order_timeout_minutes', '30'),
('low_stock_alert_enabled', 'true')
ON CONFLICT (key) DO NOTHING;
