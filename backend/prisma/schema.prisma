// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用戶模型 (員工、管理員)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  name      String
  role      UserRole @default(CASHIER)
  isActive  Boolean  @default(true)
  storeId   String?
  store     Store?   @relation(fields: [storeId], references: [id])
  
  // 關聯的訂單
  orders    Order[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum UserRole {
  ADMIN
  MANAGER
  CASHIER
  KITCHEN
}

// 門店模型
model Store {
  id          String  @id @default(cuid())
  name        String
  address     String?
  phone       String?
  email       String?
  isActive    Boolean @default(true)
  
  // 關聯
  users       User[]
  categories  Category[]
  menuItems   MenuItem[]
  orders      Order[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("stores")
}

// 菜品分類
model Category {
  id          String     @id @default(cuid())
  name        String
  description String?
  sortOrder   Int        @default(0)
  isActive    Boolean    @default(true)
  storeId     String
  store       Store      @relation(fields: [storeId], references: [id])
  
  // 關聯的菜品
  menuItems   MenuItem[]
  
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  @@map("categories")
}

// 菜品模型
model MenuItem {
  id          String  @id @default(cuid())
  name        String
  description String?
  price       Decimal @db.Decimal(10, 2)
  cost        Decimal? @db.Decimal(10, 2)
  image       String?
  isActive    Boolean @default(true)
  isAvailable Boolean @default(true)
  
  // 分類關聯
  categoryId  String
  category    Category @relation(fields: [categoryId], references: [id])
  
  // 門店關聯
  storeId     String
  store       Store    @relation(fields: [storeId], references: [id])
  
  // 庫存
  stock       Int?     @default(0)
  lowStockAlert Int?   @default(5)
  
  // 製作時間 (分鐘)
  prepTime    Int?     @default(5)
  
  // 關聯的訂單項目
  orderItems  OrderItem[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("menu_items")
}

// 訂單模型
model Order {
  id            String      @id @default(cuid())
  orderNumber   String      @unique
  status        OrderStatus @default(PENDING)
  
  // 客戶資訊
  customerName  String?
  customerPhone String?
  
  // 訂單金額
  subtotal      Decimal     @db.Decimal(10, 2)
  tax           Decimal     @db.Decimal(10, 2) @default(0)
  discount      Decimal     @db.Decimal(10, 2) @default(0)
  total         Decimal     @db.Decimal(10, 2)
  
  // 支付資訊
  paymentMethod PaymentMethod?
  paymentStatus PaymentStatus @default(PENDING)
  paidAt        DateTime?
  
  // 訂單類型
  orderType     OrderType   @default(DINE_IN)
  
  // 備註
  notes         String?
  
  // 時間戳記
  orderTime     DateTime    @default(now())
  estimatedTime DateTime?
  completedAt   DateTime?
  
  // 關聯
  storeId       String
  store         Store       @relation(fields: [storeId], references: [id])
  
  userId        String
  user          User        @relation(fields: [userId], references: [id])
  
  orderItems    OrderItem[]
  
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("orders")
}

enum OrderStatus {
  PENDING     // 待處理
  CONFIRMED   // 已確認
  PREPARING   // 製作中
  READY       // 已完成
  DELIVERED   // 已送達
  CANCELLED   // 已取消
}

enum PaymentMethod {
  CASH
  CREDIT_CARD
  MOBILE_PAYMENT
  BANK_TRANSFER
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum OrderType {
  DINE_IN
  TAKEAWAY
  DELIVERY
}

// 訂單項目模型
model OrderItem {
  id         String   @id @default(cuid())
  quantity   Int
  unitPrice  Decimal  @db.Decimal(10, 2)
  totalPrice Decimal  @db.Decimal(10, 2)
  notes      String?
  
  // 關聯
  orderId    String
  order      Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  menuItemId String
  menuItem   MenuItem @relation(fields: [menuItemId], references: [id])
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("order_items")
}

// 庫存記錄
model InventoryLog {
  id          String          @id @default(cuid())
  type        InventoryType
  quantity    Int
  reason      String?

  // 關聯
  menuItemId  String

  userId      String

  createdAt   DateTime        @default(now())

  @@map("inventory_logs")
}

enum InventoryType {
  IN      // 入庫
  OUT     // 出庫
  ADJUST  // 調整
}

// 系統設定
model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value String
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}
