# 早餐店 POS 系統 - 後端 API

## 快速開始

### 環境要求
- Node.js 18+
- PostgreSQL 15+
- Redis 7+ (可選)

### 安裝依賴
```bash
npm install
```

### 環境配置
1. 複製環境變數文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的配置

### 資料庫設置
1. 生成 Prisma 客戶端：
```bash
npm run db:generate
```

2. 運行資料庫遷移：
```bash
npm run db:migrate
```

3. 初始化種子數據：
```bash
npm run db:seed
```

### 開發模式運行
```bash
npm run dev
```

### 生產模式運行
```bash
npm run build
npm start
```

## API 文檔

### 認證端點
- `POST /api/auth/login` - 用戶登入
- `POST /api/auth/register` - 用戶註冊
- `POST /api/auth/logout` - 用戶登出
- `GET /api/auth/me` - 獲取當前用戶信息
- `PUT /api/auth/change-password` - 修改密碼

### 用戶管理
- `GET /api/users` - 獲取用戶列表
- `GET /api/users/:id` - 獲取用戶詳情
- `POST /api/users` - 創建用戶
- `PUT /api/users/:id` - 更新用戶
- `DELETE /api/users/:id` - 刪除用戶

### 門店管理
- `GET /api/stores` - 獲取門店列表
- `GET /api/stores/:id` - 獲取門店詳情
- `POST /api/stores` - 創建門店
- `PUT /api/stores/:id` - 更新門店
- `DELETE /api/stores/:id` - 刪除門店

### 菜品分類
- `GET /api/categories` - 獲取分類列表
- `GET /api/categories/:id` - 獲取分類詳情
- `POST /api/categories` - 創建分類
- `PUT /api/categories/:id` - 更新分類
- `DELETE /api/categories/:id` - 刪除分類

### 菜品管理
- `GET /api/menu-items` - 獲取菜品列表
- `GET /api/menu-items/:id` - 獲取菜品詳情
- `POST /api/menu-items` - 創建菜品
- `PUT /api/menu-items/:id` - 更新菜品
- `DELETE /api/menu-items/:id` - 刪除菜品

### 訂單管理
- `GET /api/orders` - 獲取訂單列表
- `GET /api/orders/:id` - 獲取訂單詳情
- `POST /api/orders` - 創建訂單
- `PUT /api/orders/:id` - 更新訂單
- `PUT /api/orders/:id/status` - 更新訂單狀態
- `PUT /api/orders/:id/payment` - 處理支付
- `DELETE /api/orders/:id` - 取消訂單

### 系統設定
- `GET /api/settings` - 獲取系統設定
- `GET /api/settings/:key` - 獲取特定設定
- `POST /api/settings` - 創建設定
- `PUT /api/settings/:key` - 更新設定
- `DELETE /api/settings/:key` - 刪除設定

## Socket.IO 事件

### 連接事件
- `connection` - 客戶端連接
- `disconnect` - 客戶端斷開連接
- `authenticate` - 認證
- `authenticated` - 認證成功

### 訂單事件
- `order_created` - 新訂單創建
- `order_updated` - 訂單更新
- `order_status_changed` - 訂單狀態變更
- `kds_order_received` - KDS 接收訂單
- `pos_order_ready` - 訂單完成通知 POS

### 庫存事件
- `inventory_low_stock` - 低庫存警告
- `inventory_out_of_stock` - 缺貨警告

## 預設帳號

初始化後會創建以下預設帳號：

- **管理員**: <EMAIL> / admin123
- **收銀員**: <EMAIL> / cashier123
- **廚房**: <EMAIL> / kitchen123

## 開發工具

### 資料庫管理
```bash
# 打開 Prisma Studio
npm run db:studio

# 重置資料庫
npm run db:reset
```

### 代碼檢查
```bash
# ESLint 檢查
npm run lint

# 自動修復
npm run lint:fix

# 代碼格式化
npm run format
```

### 測試
```bash
# 運行測試
npm test

# 監視模式
npm run test:watch

# 覆蓋率報告
npm run test:coverage
```

## Docker 部署

### 開發環境
```bash
docker-compose -f ../docker-compose.dev.yml up
```

### 生產環境
```bash
docker-compose -f ../docker-compose.prod.yml up -d
```
