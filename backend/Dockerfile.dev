FROM node:18-alpine

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# 複製 package.json 和 package-lock.json
COPY package*.json ./

# 安裝依賴
RUN npm ci

# 複製源代碼
COPY . .

# 生成 Prisma 客戶端
RUN npx prisma generate

# 創建必要的目錄
RUN mkdir -p uploads logs

# 暴露端口
EXPOSE 3001

# 啟動命令
CMD ["npm", "run", "dev"]
