import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import { createServer } from 'http'
import { Server } from 'socket.io'
import dotenv from 'dotenv'

import { errorHandler } from '@/middleware/errorHandler'
import { notFoundHandler } from '@/middleware/notFoundHandler'
import { authMiddleware } from '@/middleware/auth'
import { logger } from '@/utils/logger'
import { prisma } from '@/config/database'
import { setupSocketIO } from '@/config/socket'

// 路由導入
import authRoutes from '@/routes/auth'
import userRoutes from '@/routes/users'
import storeRoutes from '@/routes/stores'
import categoryRoutes from '@/routes/categories'
import menuItemRoutes from '@/routes/menuItems'
import orderRoutes from '@/routes/orders'
import settingRoutes from '@/routes/settings'

// 載入環境變數
dotenv.config()

const app = express()
const server = createServer(app)
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    credentials: true,
  },
})

const PORT = process.env.PORT || 3001

// 基礎中間件
app.use(helmet())
app.use(compression())
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
}))

// 請求限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分鐘
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 限制每個IP 100個請求
  message: {
    error: '請求過於頻繁，請稍後再試',
  },
})
app.use('/api', limiter)

// 日誌中間件
app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim()),
  },
}))

// 解析中間件
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 靜態文件服務
app.use('/uploads', express.static('uploads'))

// 健康檢查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  })
})

// API 路由
app.use('/api/auth', authRoutes)
app.use('/api/users', authMiddleware, userRoutes)
app.use('/api/stores', authMiddleware, storeRoutes)
app.use('/api/categories', authMiddleware, categoryRoutes)
app.use('/api/menu-items', authMiddleware, menuItemRoutes)
app.use('/api/orders', authMiddleware, orderRoutes)
app.use('/api/settings', authMiddleware, settingRoutes)

// Socket.IO 設置
setupSocketIO(io)

// 錯誤處理中間件
app.use(notFoundHandler)
app.use(errorHandler)

// 優雅關閉
process.on('SIGTERM', async () => {
  logger.info('收到 SIGTERM 信號，開始優雅關閉...')
  
  server.close(() => {
    logger.info('HTTP 服務器已關閉')
  })
  
  await prisma.$disconnect()
  logger.info('資料庫連接已關閉')
  
  process.exit(0)
})

process.on('SIGINT', async () => {
  logger.info('收到 SIGINT 信號，開始優雅關閉...')
  
  server.close(() => {
    logger.info('HTTP 服務器已關閉')
  })
  
  await prisma.$disconnect()
  logger.info('資料庫連接已關閉')
  
  process.exit(0)
})

// 未捕獲的異常處理
process.on('uncaughtException', (error) => {
  logger.error('未捕獲的異常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未處理的 Promise 拒絕:', reason)
  logger.error('Promise:', promise)
  process.exit(1)
})

// 啟動服務器
server.listen(PORT, () => {
  logger.info(`🚀 服務器運行在端口 ${PORT}`)
  logger.info(`📊 環境: ${process.env.NODE_ENV || 'development'}`)
  logger.info(`🔗 API 基礎路徑: http://localhost:${PORT}/api`)
  logger.info(`🔌 Socket.IO 已啟用`)
})

export { app, io }
