import { Router } from 'express'
import { body, param } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { requireManager, requireStore } from '@/middleware/auth'

const router = Router()

// 分類驗證規則
const createCategoryValidation = [
  body('name').isLength({ min: 1, max: 50 }).withMessage('分類名稱長度必須在1-50個字符之間').trim(),
  body('description').optional().isLength({ max: 200 }).withMessage('描述長度不能超過200個字符').trim(),
  body('sortOrder').optional().isInt({ min: 0 }).withMessage('排序順序必須是非負整數'),
]

const updateCategoryValidation = [
  param('id').isString().withMessage('分類ID必須是字符串'),
  body('name').optional().isLength({ min: 1, max: 50 }).withMessage('分類名稱長度必須在1-50個字符之間').trim(),
  body('description').optional().isLength({ max: 200 }).withMessage('描述長度不能超過200個字符').trim(),
  body('sortOrder').optional().isInt({ min: 0 }).withMessage('排序順序必須是非負整數'),
  body('isActive').optional().isBoolean().withMessage('isActive必須是布爾值'),
]

// 路由定義 (暫時返回空實現)
router.get('/', requireStore, (req, res) => {
  res.json({ success: true, message: '獲取分類列表 - 待實現' })
})

router.get('/:id', requireStore, (req, res) => {
  res.json({ success: true, message: '獲取分類詳情 - 待實現' })
})

router.post('/', requireManager, requireStore, createCategoryValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '創建分類 - 待實現' })
})

router.put('/:id', requireManager, updateCategoryValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '更新分類 - 待實現' })
})

router.delete('/:id', requireManager, (req, res) => {
  res.json({ success: true, message: '刪除分類 - 待實現' })
})

export default router
