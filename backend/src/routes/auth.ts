import { Router } from 'express'
import { body } from 'express-validator'
import { authController } from '@/controllers/authController'
import { validateRequest } from '@/middleware/validation'
import { authMiddleware } from '@/middleware/auth'

const router = Router()

// 登入驗證規則
const loginValidation = [
  body('email')
    .isEmail()
    .withMessage('請輸入有效的電子郵件地址')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密碼至少需要6個字符'),
]

// 註冊驗證規則
const registerValidation = [
  body('email')
    .isEmail()
    .withMessage('請輸入有效的電子郵件地址')
    .normalizeEmail(),
  body('username')
    .isLength({ min: 3, max: 20 })
    .withMessage('用戶名長度必須在3-20個字符之間')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用戶名只能包含字母、數字和下劃線'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密碼至少需要6個字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密碼必須包含至少一個小寫字母、一個大寫字母和一個數字'),
  body('name')
    .isLength({ min: 2, max: 50 })
    .withMessage('姓名長度必須在2-50個字符之間')
    .trim(),
  body('role')
    .optional()
    .isIn(['ADMIN', 'MANAGER', 'CASHIER', 'KITCHEN'])
    .withMessage('無效的用戶角色'),
]

// 修改密碼驗證規則
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('請輸入當前密碼'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密碼至少需要6個字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密碼必須包含至少一個小寫字母、一個大寫字母和一個數字'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('確認密碼與新密碼不匹配')
      }
      return true
    }),
]

// 路由定義
router.post('/login', loginValidation, validateRequest, authController.login)
router.post('/register', registerValidation, validateRequest, authController.register)
router.post('/logout', authMiddleware, authController.logout)
router.post('/refresh', authController.refreshToken)
router.get('/me', authMiddleware, authController.getCurrentUser)
router.put('/change-password', authMiddleware, changePasswordValidation, validateRequest, authController.changePassword)
router.post('/forgot-password', [
  body('email').isEmail().withMessage('請輸入有效的電子郵件地址').normalizeEmail(),
], validateRequest, authController.forgotPassword)
router.post('/reset-password', [
  body('token').notEmpty().withMessage('重置令牌不能為空'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密碼至少需要6個字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密碼必須包含至少一個小寫字母、一個大寫字母和一個數字'),
], validateRequest, authController.resetPassword)

export default router
