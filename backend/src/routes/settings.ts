import { Router } from 'express'
import { body, param } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { requireAdmin, requireManager } from '@/middleware/auth'

const router = Router()

// 設定驗證規則
const updateSettingValidation = [
  param('key').isString().withMessage('設定鍵必須是字符串'),
  body('value').isString().withMessage('設定值必須是字符串'),
]

const createSettingValidation = [
  body('key').isString().withMessage('設定鍵必須是字符串'),
  body('value').isString().withMessage('設定值必須是字符串'),
]

// 路由定義 (暫時返回空實現)
router.get('/', requireManager, (req, res) => {
  res.json({ success: true, message: '獲取系統設定 - 待實現' })
})

router.get('/:key', requireManager, (req, res) => {
  res.json({ success: true, message: '獲取特定設定 - 待實現' })
})

router.post('/', requireAdmin, createSettingValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '創建設定 - 待實現' })
})

router.put('/:key', requireAdmin, updateSettingValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '更新設定 - 待實現' })
})

router.delete('/:key', requireAdmin, (req, res) => {
  res.json({ success: true, message: '刪除設定 - 待實現' })
})

export default router
