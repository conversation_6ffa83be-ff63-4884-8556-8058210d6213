import { Router } from 'express'
import { body, param } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { requireAdmin, requireManager } from '@/middleware/auth'

const router = Router()

// 門店驗證規則
const createStoreValidation = [
  body('name').isLength({ min: 2, max: 100 }).withMessage('門店名稱長度必須在2-100個字符之間').trim(),
  body('address').optional().isLength({ max: 200 }).withMessage('地址長度不能超過200個字符').trim(),
  body('phone').optional().isMobilePhone('zh-TW').withMessage('請輸入有效的電話號碼'),
  body('email').optional().isEmail().withMessage('請輸入有效的電子郵件地址').normalizeEmail(),
]

const updateStoreValidation = [
  param('id').isString().withMessage('門店ID必須是字符串'),
  body('name').optional().isLength({ min: 2, max: 100 }).withMessage('門店名稱長度必須在2-100個字符之間').trim(),
  body('address').optional().isLength({ max: 200 }).withMessage('地址長度不能超過200個字符').trim(),
  body('phone').optional().isMobilePhone('zh-TW').withMessage('請輸入有效的電話號碼'),
  body('email').optional().isEmail().withMessage('請輸入有效的電子郵件地址').normalizeEmail(),
  body('isActive').optional().isBoolean().withMessage('isActive必須是布爾值'),
]

// 路由定義 (暫時返回空實現)
router.get('/', requireManager, (req, res) => {
  res.json({ success: true, message: '獲取門店列表 - 待實現' })
})

router.get('/:id', requireManager, (req, res) => {
  res.json({ success: true, message: '獲取門店詳情 - 待實現' })
})

router.post('/', requireAdmin, createStoreValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '創建門店 - 待實現' })
})

router.put('/:id', requireAdmin, updateStoreValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '更新門店 - 待實現' })
})

router.delete('/:id', requireAdmin, (req, res) => {
  res.json({ success: true, message: '刪除門店 - 待實現' })
})

export default router
