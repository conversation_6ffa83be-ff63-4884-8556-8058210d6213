import { Router } from 'express'
import { body, param } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { requireManager, requireStore } from '@/middleware/auth'

const router = Router()

// 菜品驗證規則
const createMenuItemValidation = [
  body('name').isLength({ min: 1, max: 100 }).withMessage('菜品名稱長度必須在1-100個字符之間').trim(),
  body('description').optional().isLength({ max: 500 }).withMessage('描述長度不能超過500個字符').trim(),
  body('price').isFloat({ min: 0 }).withMessage('價格必須是非負數'),
  body('cost').optional().isFloat({ min: 0 }).withMessage('成本必須是非負數'),
  body('categoryId').isString().withMessage('分類ID必須是字符串'),
  body('stock').optional().isInt({ min: 0 }).withMessage('庫存必須是非負整數'),
  body('lowStockAlert').optional().isInt({ min: 0 }).withMessage('低庫存警告必須是非負整數'),
  body('prepTime').optional().isInt({ min: 1 }).withMessage('製作時間必須是正整數'),
]

const updateMenuItemValidation = [
  param('id').isString().withMessage('菜品ID必須是字符串'),
  body('name').optional().isLength({ min: 1, max: 100 }).withMessage('菜品名稱長度必須在1-100個字符之間').trim(),
  body('description').optional().isLength({ max: 500 }).withMessage('描述長度不能超過500個字符').trim(),
  body('price').optional().isFloat({ min: 0 }).withMessage('價格必須是非負數'),
  body('cost').optional().isFloat({ min: 0 }).withMessage('成本必須是非負數'),
  body('categoryId').optional().isString().withMessage('分類ID必須是字符串'),
  body('stock').optional().isInt({ min: 0 }).withMessage('庫存必須是非負整數'),
  body('lowStockAlert').optional().isInt({ min: 0 }).withMessage('低庫存警告必須是非負整數'),
  body('prepTime').optional().isInt({ min: 1 }).withMessage('製作時間必須是正整數'),
  body('isActive').optional().isBoolean().withMessage('isActive必須是布爾值'),
  body('isAvailable').optional().isBoolean().withMessage('isAvailable必須是布爾值'),
]

// 路由定義 (暫時返回空實現)
router.get('/', requireStore, (req, res) => {
  res.json({ success: true, message: '獲取菜品列表 - 待實現' })
})

router.get('/:id', requireStore, (req, res) => {
  res.json({ success: true, message: '獲取菜品詳情 - 待實現' })
})

router.post('/', requireManager, requireStore, createMenuItemValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '創建菜品 - 待實現' })
})

router.put('/:id', requireManager, updateMenuItemValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '更新菜品 - 待實現' })
})

router.delete('/:id', requireManager, (req, res) => {
  res.json({ success: true, message: '刪除菜品 - 待實現' })
})

export default router
