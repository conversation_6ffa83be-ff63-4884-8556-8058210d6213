import { Router } from 'express'
import { body, param, query } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { requireCashier, requireKitchen, requireStore } from '@/middleware/auth'

const router = Router()

// 訂單驗證規則
const createOrderValidation = [
  body('orderItems').isArray({ min: 1 }).withMessage('訂單項目不能為空'),
  body('orderItems.*.menuItemId').isString().withMessage('菜品ID必須是字符串'),
  body('orderItems.*.quantity').isInt({ min: 1 }).withMessage('數量必須是正整數'),
  body('orderItems.*.notes').optional().isLength({ max: 200 }).withMessage('備註長度不能超過200個字符'),
  body('customerName').optional().isLength({ max: 50 }).withMessage('客戶姓名長度不能超過50個字符').trim(),
  body('customerPhone').optional().isMobilePhone('zh-TW').withMessage('請輸入有效的電話號碼'),
  body('orderType').isIn(['DINE_IN', 'TAKEAWAY', 'DELIVERY']).withMessage('無效的訂單類型'),
  body('notes').optional().isLength({ max: 500 }).withMessage('備註長度不能超過500個字符').trim(),
]

const updateOrderValidation = [
  param('id').isString().withMessage('訂單ID必須是字符串'),
  body('status').optional().isIn(['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED']).withMessage('無效的訂單狀態'),
  body('paymentMethod').optional().isIn(['CASH', 'CREDIT_CARD', 'MOBILE_PAYMENT', 'BANK_TRANSFER']).withMessage('無效的支付方式'),
  body('paymentStatus').optional().isIn(['PENDING', 'PAID', 'FAILED', 'REFUNDED']).withMessage('無效的支付狀態'),
  body('notes').optional().isLength({ max: 500 }).withMessage('備註長度不能超過500個字符').trim(),
]

// 路由定義 (暫時返回空實現)
router.get('/', requireStore, (req, res) => {
  res.json({ success: true, message: '獲取訂單列表 - 待實現' })
})

router.get('/:id', requireStore, (req, res) => {
  res.json({ success: true, message: '獲取訂單詳情 - 待實現' })
})

router.post('/', requireCashier, requireStore, createOrderValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '創建訂單 - 待實現' })
})

router.put('/:id', requireStore, updateOrderValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '更新訂單 - 待實現' })
})

router.put('/:id/status', requireStore, (req, res) => {
  res.json({ success: true, message: '更新訂單狀態 - 待實現' })
})

router.put('/:id/payment', requireCashier, (req, res) => {
  res.json({ success: true, message: '處理訂單支付 - 待實現' })
})

router.delete('/:id', requireCashier, (req, res) => {
  res.json({ success: true, message: '取消訂單 - 待實現' })
})

export default router
