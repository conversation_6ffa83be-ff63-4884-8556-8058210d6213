import { Router } from 'express'
import { body, param, query } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { requireCashier, requireKitchen, requireStore } from '@/middleware/auth'
import { orderController } from '@/controllers/orderController'

const router = Router()

// 訂單驗證規則
const createOrderValidation = [
  body('orderItems').isArray({ min: 1 }).withMessage('訂單項目不能為空'),
  body('orderItems.*.menuItemId').isString().withMessage('菜品ID必須是字符串'),
  body('orderItems.*.quantity').isInt({ min: 1 }).withMessage('數量必須是正整數'),
  body('orderItems.*.notes').optional().isLength({ max: 200 }).withMessage('備註長度不能超過200個字符'),
  body('customerName').optional().isLength({ max: 50 }).withMessage('客戶姓名長度不能超過50個字符').trim(),
  body('customerPhone').optional().isMobilePhone('zh-TW').withMessage('請輸入有效的電話號碼'),
  body('orderType').isIn(['DINE_IN', 'TAKEAWAY', 'DELIVERY']).withMessage('無效的訂單類型'),
  body('notes').optional().isLength({ max: 500 }).withMessage('備註長度不能超過500個字符').trim(),
]

const updateOrderStatusValidation = [
  param('id').isString().withMessage('訂單ID必須是字符串'),
  body('status').isIn(['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED']).withMessage('無效的訂單狀態'),
  body('notes').optional().isLength({ max: 500 }).withMessage('備註長度不能超過500個字符').trim(),
]

const cancelOrderValidation = [
  param('id').isString().withMessage('訂單ID必須是字符串'),
  body('reason').optional().isLength({ max: 200 }).withMessage('取消原因長度不能超過200個字符').trim(),
]

// 路由定義
router.get('/', requireStore, orderController.getOrders)
router.get('/kitchen', requireKitchen, requireStore, orderController.getKitchenOrders)
router.get('/:id', requireStore, orderController.getOrder)
router.post('/', requireCashier, requireStore, createOrderValidation, validateRequest, orderController.createOrder)
router.put('/:id/status', requireStore, updateOrderStatusValidation, validateRequest, orderController.updateOrderStatus)
router.delete('/:id', requireCashier, cancelOrderValidation, validateRequest, orderController.cancelOrder)

export default router
