import { Router } from 'express'
import { body, param, query } from 'express-validator'
import { validateRequest } from '@/middleware/validation'
import { requireAdmin, requireManager } from '@/middleware/auth'

const router = Router()

// 用戶驗證規則
const createUserValidation = [
  body('email').isEmail().withMessage('請輸入有效的電子郵件地址').normalizeEmail(),
  body('username').isLength({ min: 3, max: 20 }).withMessage('用戶名長度必須在3-20個字符之間'),
  body('password').isLength({ min: 6 }).withMessage('密碼至少需要6個字符'),
  body('name').isLength({ min: 2, max: 50 }).withMessage('姓名長度必須在2-50個字符之間').trim(),
  body('role').isIn(['ADMIN', 'MANAGER', 'CASHIER', 'KITCHEN']).withMessage('無效的用戶角色'),
]

const updateUserValidation = [
  param('id').isString().withMessage('用戶ID必須是字符串'),
  body('email').optional().isEmail().withMessage('請輸入有效的電子郵件地址').normalizeEmail(),
  body('username').optional().isLength({ min: 3, max: 20 }).withMessage('用戶名長度必須在3-20個字符之間'),
  body('name').optional().isLength({ min: 2, max: 50 }).withMessage('姓名長度必須在2-50個字符之間').trim(),
  body('role').optional().isIn(['ADMIN', 'MANAGER', 'CASHIER', 'KITCHEN']).withMessage('無效的用戶角色'),
  body('isActive').optional().isBoolean().withMessage('isActive必須是布爾值'),
]

// 路由定義 (暫時返回空實現)
router.get('/', requireManager, (req, res) => {
  res.json({ success: true, message: '獲取用戶列表 - 待實現' })
})

router.get('/:id', requireManager, (req, res) => {
  res.json({ success: true, message: '獲取用戶詳情 - 待實現' })
})

router.post('/', requireAdmin, createUserValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '創建用戶 - 待實現' })
})

router.put('/:id', requireAdmin, updateUserValidation, validateRequest, (req, res) => {
  res.json({ success: true, message: '更新用戶 - 待實現' })
})

router.delete('/:id', requireAdmin, (req, res) => {
  res.json({ success: true, message: '刪除用戶 - 待實現' })
})

export default router
