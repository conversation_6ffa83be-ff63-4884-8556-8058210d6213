import { Server, Socket } from 'socket.io'
import jwt from 'jsonwebtoken'
import { logger } from '@/utils/logger'
import { prisma } from '@/config/database'

interface AuthenticatedSocket extends Socket {
  userId?: string
  userRole?: string
  storeId?: string
}

// Socket.IO 事件類型
export enum SocketEvents {
  // 連接事件
  CONNECTION = 'connection',
  DISCONNECT = 'disconnect',
  
  // 認證事件
  AUTHENTICATE = 'authenticate',
  AUTHENTICATED = 'authenticated',
  AUTHENTICATION_ERROR = 'authentication_error',
  
  // 訂單事件
  ORDER_CREATED = 'order_created',
  ORDER_UPDATED = 'order_updated',
  ORDER_STATUS_CHANGED = 'order_status_changed',
  ORDER_CANCELLED = 'order_cancelled',
  
  // KDS 事件
  KDS_ORDER_RECEIVED = 'kds_order_received',
  KDS_ORDER_STARTED = 'kds_order_started',
  KDS_ORDER_COMPLETED = 'kds_order_completed',
  
  // POS 事件
  POS_ORDER_READY = 'pos_order_ready',
  POS_PAYMENT_COMPLETED = 'pos_payment_completed',
  
  // 庫存事件
  INVENTORY_LOW_STOCK = 'inventory_low_stock',
  INVENTORY_OUT_OF_STOCK = 'inventory_out_of_stock',
  
  // 系統事件
  SYSTEM_NOTIFICATION = 'system_notification',
  STORE_STATUS_CHANGED = 'store_status_changed',
}

// Socket.IO 房間
export enum SocketRooms {
  STORE_PREFIX = 'store_',
  POS_PREFIX = 'pos_',
  KDS_PREFIX = 'kds_',
  ADMIN_PREFIX = 'admin_',
}

export const setupSocketIO = (io: Server) => {
  // 認證中間件
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')
      
      if (!token) {
        return next(new Error('認證令牌缺失'))
      }
      
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
      
      // 驗證用戶是否存在且啟用
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        include: { store: true },
      })
      
      if (!user || !user.isActive) {
        return next(new Error('用戶不存在或已停用'))
      }
      
      socket.userId = user.id
      socket.userRole = user.role
      socket.storeId = user.storeId || undefined
      
      next()
    } catch (error) {
      logger.error('Socket 認證失敗:', error)
      next(new Error('認證失敗'))
    }
  })
  
  io.on(SocketEvents.CONNECTION, (socket: AuthenticatedSocket) => {
    logger.info(`用戶 ${socket.userId} 已連接 Socket.IO`)
    
    // 加入相應的房間
    if (socket.storeId) {
      socket.join(`${SocketRooms.STORE_PREFIX}${socket.storeId}`)
      
      // 根據用戶角色加入特定房間
      switch (socket.userRole) {
        case 'ADMIN':
        case 'MANAGER':
          socket.join(`${SocketRooms.ADMIN_PREFIX}${socket.storeId}`)
          break
        case 'CASHIER':
          socket.join(`${SocketRooms.POS_PREFIX}${socket.storeId}`)
          break
        case 'KITCHEN':
          socket.join(`${SocketRooms.KDS_PREFIX}${socket.storeId}`)
          break
      }
    }
    
    // 發送認證成功事件
    socket.emit(SocketEvents.AUTHENTICATED, {
      userId: socket.userId,
      userRole: socket.userRole,
      storeId: socket.storeId,
    })
    
    // 訂單狀態更新事件
    socket.on(SocketEvents.ORDER_STATUS_CHANGED, async (data) => {
      try {
        const { orderId, status, userId } = data
        
        // 驗證權限
        if (!socket.storeId) {
          socket.emit('error', { message: '無權限操作' })
          return
        }
        
        // 更新訂單狀態
        const updatedOrder = await prisma.order.update({
          where: { id: orderId },
          data: { status },
          include: {
            orderItems: {
              include: {
                menuItem: true,
              },
            },
            user: true,
          },
        })
        
        // 廣播到相關房間
        io.to(`${SocketRooms.STORE_PREFIX}${socket.storeId}`).emit(SocketEvents.ORDER_UPDATED, updatedOrder)
        
        // 特定狀態的額外處理
        switch (status) {
          case 'PREPARING':
            io.to(`${SocketRooms.KDS_PREFIX}${socket.storeId}`).emit(SocketEvents.KDS_ORDER_STARTED, updatedOrder)
            break
          case 'READY':
            io.to(`${SocketRooms.POS_PREFIX}${socket.storeId}`).emit(SocketEvents.POS_ORDER_READY, updatedOrder)
            break
        }
        
        logger.info(`訂單 ${orderId} 狀態已更新為 ${status}`)
      } catch (error) {
        logger.error('更新訂單狀態失敗:', error)
        socket.emit('error', { message: '更新訂單狀態失敗' })
      }
    })
    
    // 新訂單創建事件
    socket.on(SocketEvents.ORDER_CREATED, (orderData) => {
      if (socket.storeId) {
        // 廣播新訂單到 KDS
        io.to(`${SocketRooms.KDS_PREFIX}${socket.storeId}`).emit(SocketEvents.KDS_ORDER_RECEIVED, orderData)
        
        // 廣播到管理員
        io.to(`${SocketRooms.ADMIN_PREFIX}${socket.storeId}`).emit(SocketEvents.ORDER_CREATED, orderData)
        
        logger.info(`新訂單 ${orderData.id} 已廣播到 KDS`)
      }
    })
    
    // 庫存警告事件
    socket.on(SocketEvents.INVENTORY_LOW_STOCK, (inventoryData) => {
      if (socket.storeId) {
        io.to(`${SocketRooms.ADMIN_PREFIX}${socket.storeId}`).emit(SocketEvents.INVENTORY_LOW_STOCK, inventoryData)
        logger.info(`低庫存警告: ${inventoryData.itemName}`)
      }
    })
    
    // 系統通知事件
    socket.on(SocketEvents.SYSTEM_NOTIFICATION, (notification) => {
      if (socket.storeId) {
        io.to(`${SocketRooms.STORE_PREFIX}${socket.storeId}`).emit(SocketEvents.SYSTEM_NOTIFICATION, notification)
      }
    })
    
    // 斷開連接事件
    socket.on(SocketEvents.DISCONNECT, (reason) => {
      logger.info(`用戶 ${socket.userId} 已斷開連接: ${reason}`)
    })
    
    // 錯誤處理
    socket.on('error', (error) => {
      logger.error(`Socket 錯誤 (用戶 ${socket.userId}):`, error)
    })
  })
  
  logger.info('✅ Socket.IO 設置完成')
}

// 輔助函數：向特定門店廣播消息
export const broadcastToStore = (io: Server, storeId: string, event: string, data: any) => {
  io.to(`${SocketRooms.STORE_PREFIX}${storeId}`).emit(event, data)
}

// 輔助函數：向 POS 廣播消息
export const broadcastToPOS = (io: Server, storeId: string, event: string, data: any) => {
  io.to(`${SocketRooms.POS_PREFIX}${storeId}`).emit(event, data)
}

// 輔助函數：向 KDS 廣播消息
export const broadcastToKDS = (io: Server, storeId: string, event: string, data: any) => {
  io.to(`${SocketRooms.KDS_PREFIX}${storeId}`).emit(event, data)
}

// 輔助函數：向管理員廣播消息
export const broadcastToAdmin = (io: Server, storeId: string, event: string, data: any) => {
  io.to(`${SocketRooms.ADMIN_PREFIX}${storeId}`).emit(event, data)
}
