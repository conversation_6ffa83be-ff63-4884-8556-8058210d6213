import { PrismaClient } from '@prisma/client'
import { logger } from '@/utils/logger'

// 創建 Prisma 客戶端實例
export const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
})

// 設置日誌事件監聽器
prisma.$on('query', (e) => {
  if (process.env.NODE_ENV === 'development') {
    logger.debug(`Query: ${e.query}`)
    logger.debug(`Params: ${e.params}`)
    logger.debug(`Duration: ${e.duration}ms`)
  }
})

prisma.$on('error', (e) => {
  logger.error('Prisma Error:', e)
})

prisma.$on('info', (e) => {
  logger.info('Prisma Info:', e.message)
})

prisma.$on('warn', (e) => {
  logger.warn('Prisma Warning:', e.message)
})

// 測試資料庫連接
export const connectDatabase = async () => {
  try {
    await prisma.$connect()
    logger.info('✅ 資料庫連接成功')
  } catch (error) {
    logger.error('❌ 資料庫連接失敗:', error)
    process.exit(1)
  }
}

// 斷開資料庫連接
export const disconnectDatabase = async () => {
  try {
    await prisma.$disconnect()
    logger.info('✅ 資料庫連接已斷開')
  } catch (error) {
    logger.error('❌ 斷開資料庫連接時發生錯誤:', error)
  }
}

// 資料庫健康檢查
export const checkDatabaseHealth = async () => {
  try {
    await prisma.$queryRaw`SELECT 1`
    return { status: 'healthy', timestamp: new Date().toISOString() }
  } catch (error) {
    logger.error('資料庫健康檢查失敗:', error)
    return { status: 'unhealthy', error: error instanceof Error ? error.message : 'Unknown error' }
  }
}
