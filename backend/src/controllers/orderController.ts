import { Request, Response } from 'express'
import { prisma } from '@/config/database'
import { catchAsync } from '@/middleware/errorHandler'
import { NotFoundError, ValidationError, ForbiddenError } from '@/middleware/errorHandler'
import { logger } from '@/utils/logger'
import { io } from '@/index'
import { broadcastToStore, broadcastToKDS, broadcastToPOS } from '@/config/socket'

// 獲取訂單列表
const getOrders = catchAsync(async (req: Request, res: Response) => {
  const { page = 1, limit = 20, status, storeId, search } = req.query
  const userStoreId = req.user?.storeId

  // 構建查詢條件
  const where: any = {}
  
  // 門店過濾
  if (userStoreId) {
    where.storeId = userStoreId
  } else if (storeId) {
    where.storeId = storeId as string
  }
  
  // 狀態過濾
  if (status) {
    const statusArray = Array.isArray(status) ? status : [status]
    where.status = { in: statusArray }
  }
  
  // 搜索過濾
  if (search) {
    where.OR = [
      { orderNumber: { contains: search as string, mode: 'insensitive' } },
      { customerName: { contains: search as string, mode: 'insensitive' } },
      { customerPhone: { contains: search as string, mode: 'insensitive' } },
    ]
  }

  const skip = (Number(page) - 1) * Number(limit)
  
  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            menuItem: {
              include: {
                category: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { orderTime: 'desc' },
      skip,
      take: Number(limit),
    }),
    prisma.order.count({ where }),
  ])

  const totalPages = Math.ceil(total / Number(limit))

  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages,
        hasNext: Number(page) < totalPages,
        hasPrev: Number(page) > 1,
      },
    },
  })
})

// 獲取單個訂單
const getOrder = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params
  const userStoreId = req.user?.storeId

  const order = await prisma.order.findUnique({
    where: { id },
    include: {
      orderItems: {
        include: {
          menuItem: {
            include: {
              category: true,
            },
          },
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          username: true,
        },
      },
      store: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  if (!order) {
    throw new NotFoundError('訂單')
  }

  // 檢查門店權限
  if (userStoreId && order.storeId !== userStoreId) {
    throw new ForbiddenError('無權限訪問此訂單')
  }

  res.json({
    success: true,
    data: { order },
  })
})

// 創建訂單
const createOrder = catchAsync(async (req: Request, res: Response) => {
  const { orderItems, customerName, customerPhone, orderType, notes } = req.body
  const userId = req.user!.id
  const storeId = req.user!.storeId

  if (!storeId) {
    throw new ValidationError('用戶未分配到門店')
  }

  if (!orderItems || orderItems.length === 0) {
    throw new ValidationError('訂單項目不能為空')
  }

  // 驗證菜品並計算價格
  let subtotal = 0
  const validatedItems = []

  for (const item of orderItems) {
    const menuItem = await prisma.menuItem.findUnique({
      where: { id: item.menuItemId },
    })

    if (!menuItem) {
      throw new NotFoundError(`菜品 ID: ${item.menuItemId}`)
    }

    if (!menuItem.isActive || !menuItem.isAvailable) {
      throw new ValidationError(`菜品 ${menuItem.name} 暫時無法供應`)
    }

    // 檢查庫存
    if (menuItem.stock !== null && menuItem.stock < item.quantity) {
      throw new ValidationError(`菜品 ${menuItem.name} 庫存不足`)
    }

    const itemTotal = menuItem.price * item.quantity
    subtotal += itemTotal

    validatedItems.push({
      menuItemId: item.menuItemId,
      quantity: item.quantity,
      unitPrice: menuItem.price,
      totalPrice: itemTotal,
      notes: item.notes || null,
    })
  }

  // 計算稅額和總計
  const taxRate = 0.05 // 5% 稅率
  const tax = subtotal * taxRate
  const total = subtotal + tax

  // 創建訂單
  const order = await prisma.order.create({
    data: {
      orderNumber: '', // 將由資料庫觸發器生成
      status: 'CONFIRMED',
      customerName,
      customerPhone,
      subtotal,
      tax,
      total,
      orderType,
      notes,
      storeId,
      userId,
      orderItems: {
        create: validatedItems,
      },
    },
    include: {
      orderItems: {
        include: {
          menuItem: {
            include: {
              category: true,
            },
          },
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          username: true,
        },
      },
      store: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  // 更新庫存
  for (const item of validatedItems) {
    await prisma.menuItem.update({
      where: { id: item.menuItemId },
      data: {
        stock: {
          decrement: item.quantity,
        },
      },
    })
  }

  logger.info(`新訂單創建: ${order.orderNumber} by ${req.user?.name}`)

  // 即時推送到 KDS
  if (io) {
    broadcastToKDS(io, storeId, 'kds_order_received', order)
    broadcastToStore(io, storeId, 'order_created', order)
  }

  res.status(201).json({
    success: true,
    message: '訂單創建成功',
    data: { order },
  })
})

// 更新訂單狀態
const updateOrderStatus = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params
  const { status, notes } = req.body
  const userId = req.user!.id
  const userStoreId = req.user!.storeId

  const existingOrder = await prisma.order.findUnique({
    where: { id },
    include: { store: true },
  })

  if (!existingOrder) {
    throw new NotFoundError('訂單')
  }

  // 檢查門店權限
  if (userStoreId && existingOrder.storeId !== userStoreId) {
    throw new ForbiddenError('無權限操作此訂單')
  }

  // 更新訂單
  const updateData: any = { status }
  
  if (notes) {
    updateData.notes = notes
  }

  if (status === 'READY') {
    updateData.completedAt = new Date()
  }

  const updatedOrder = await prisma.order.update({
    where: { id },
    data: updateData,
    include: {
      orderItems: {
        include: {
          menuItem: {
            include: {
              category: true,
            },
          },
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          username: true,
        },
      },
      store: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  logger.info(`訂單狀態更新: ${updatedOrder.orderNumber} -> ${status} by ${req.user?.name}`)

  // 即時推送狀態更新
  if (io) {
    broadcastToStore(io, existingOrder.storeId, 'order_status_changed', {
      orderId: id,
      status,
      userId,
      order: updatedOrder,
    })

    // 特定狀態的額外推送
    if (status === 'READY') {
      broadcastToPOS(io, existingOrder.storeId, 'pos_order_ready', updatedOrder)
    }
  }

  res.json({
    success: true,
    message: '訂單狀態更新成功',
    data: { order: updatedOrder },
  })
})

// 取消訂單
const cancelOrder = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params
  const { reason } = req.body
  const userId = req.user!.id
  const userStoreId = req.user!.storeId

  const existingOrder = await prisma.order.findUnique({
    where: { id },
    include: {
      orderItems: true,
    },
  })

  if (!existingOrder) {
    throw new NotFoundError('訂單')
  }

  // 檢查門店權限
  if (userStoreId && existingOrder.storeId !== userStoreId) {
    throw new ForbiddenError('無權限操作此訂單')
  }

  // 檢查訂單狀態
  if (existingOrder.status === 'CANCELLED') {
    throw new ValidationError('訂單已經被取消')
  }

  if (existingOrder.status === 'DELIVERED') {
    throw new ValidationError('已送達的訂單無法取消')
  }

  // 更新訂單狀態
  const cancelledOrder = await prisma.order.update({
    where: { id },
    data: {
      status: 'CANCELLED',
      notes: reason ? `${existingOrder.notes || ''}\n取消原因：${reason}`.trim() : existingOrder.notes,
    },
    include: {
      orderItems: {
        include: {
          menuItem: {
            include: {
              category: true,
            },
          },
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          username: true,
        },
      },
      store: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  // 恢復庫存
  for (const item of existingOrder.orderItems) {
    await prisma.menuItem.update({
      where: { id: item.menuItemId },
      data: {
        stock: {
          increment: item.quantity,
        },
      },
    })
  }

  logger.info(`訂單取消: ${cancelledOrder.orderNumber} by ${req.user?.name}, 原因: ${reason}`)

  // 即時推送取消通知
  if (io) {
    broadcastToStore(io, existingOrder.storeId, 'order_cancelled', {
      orderId: id,
      reason,
      order: cancelledOrder,
    })
  }

  res.json({
    success: true,
    message: '訂單已取消',
    data: { order: cancelledOrder },
  })
})

// 獲取廚房訂單（KDS 專用）
const getKitchenOrders = catchAsync(async (req: Request, res: Response) => {
  const userStoreId = req.user?.storeId
  const { storeId } = req.query

  const targetStoreId = userStoreId || (storeId as string)

  if (!targetStoreId) {
    throw new ValidationError('門店 ID 不能為空')
  }

  const orders = await prisma.order.findMany({
    where: {
      storeId: targetStoreId,
      status: {
        in: ['CONFIRMED', 'PREPARING', 'READY'],
      },
    },
    include: {
      orderItems: {
        include: {
          menuItem: {
            include: {
              category: true,
            },
          },
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          username: true,
        },
      },
    },
    orderBy: { orderTime: 'asc' },
  })

  res.json({
    success: true,
    data: { orders },
  })
})

export const orderController = {
  getOrders,
  getOrder,
  createOrder,
  updateOrderStatus,
  cancelOrder,
  getKitchenOrders,
}
