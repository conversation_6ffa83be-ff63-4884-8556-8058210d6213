import { Request, Response } from 'express'
import { prisma } from '@/config/database'
import { catchAsync } from '@/middleware/errorHandler'
import { NotFoundError, ValidationError, ForbiddenError } from '@/middleware/errorHandler'
import { logger } from '@/utils/logger'

// 獲取菜品列表
const getMenuItems = catchAsync(async (req: Request, res: Response) => {
  const { page = 1, limit = 50, categoryId, search, isActive, isAvailable } = req.query
  const userStoreId = req.user?.storeId

  if (!userStoreId) {
    throw new ValidationError('用戶未分配到門店')
  }

  // 構建查詢條件
  const where: any = {
    storeId: userStoreId,
  }

  if (categoryId) {
    where.categoryId = categoryId as string
  }

  if (isActive !== undefined) {
    where.isActive = isActive === 'true'
  }

  if (isAvailable !== undefined) {
    where.isAvailable = isAvailable === 'true'
  }

  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
    ]
  }

  const skip = (Number(page) - 1) * Number(limit)

  const [menuItems, total] = await Promise.all([
    prisma.menuItem.findMany({
      where,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            sortOrder: true,
          },
        },
      },
      orderBy: [
        { category: { sortOrder: 'asc' } },
        { name: 'asc' },
      ],
      skip,
      take: Number(limit),
    }),
    prisma.menuItem.count({ where }),
  ])

  const totalPages = Math.ceil(total / Number(limit))

  res.json({
    success: true,
    data: {
      menuItems,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages,
        hasNext: Number(page) < totalPages,
        hasPrev: Number(page) > 1,
      },
    },
  })
})

// 獲取單個菜品
const getMenuItem = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params
  const userStoreId = req.user?.storeId

  const menuItem = await prisma.menuItem.findUnique({
    where: { id },
    include: {
      category: {
        select: {
          id: true,
          name: true,
          description: true,
          sortOrder: true,
        },
      },
    },
  })

  if (!menuItem) {
    throw new NotFoundError('菜品')
  }

  // 檢查門店權限
  if (userStoreId && menuItem.storeId !== userStoreId) {
    throw new ForbiddenError('無權限訪問此菜品')
  }

  res.json({
    success: true,
    data: { menuItem },
  })
})

// 創建菜品
const createMenuItem = catchAsync(async (req: Request, res: Response) => {
  const {
    name,
    description,
    price,
    cost,
    categoryId,
    stock,
    lowStockAlert,
    prepTime,
    image,
  } = req.body
  const userStoreId = req.user?.storeId

  if (!userStoreId) {
    throw new ValidationError('用戶未分配到門店')
  }

  // 驗證分類是否存在且屬於同一門店
  const category = await prisma.category.findUnique({
    where: { id: categoryId },
  })

  if (!category) {
    throw new NotFoundError('菜品分類')
  }

  if (category.storeId !== userStoreId) {
    throw new ForbiddenError('無權限使用此分類')
  }

  // 檢查同名菜品
  const existingMenuItem = await prisma.menuItem.findFirst({
    where: {
      name,
      storeId: userStoreId,
    },
  })

  if (existingMenuItem) {
    throw new ValidationError('菜品名稱已存在')
  }

  const menuItem = await prisma.menuItem.create({
    data: {
      name,
      description,
      price,
      cost,
      categoryId,
      storeId: userStoreId,
      stock,
      lowStockAlert,
      prepTime,
      image,
    },
    include: {
      category: {
        select: {
          id: true,
          name: true,
          description: true,
          sortOrder: true,
        },
      },
    },
  })

  logger.info(`菜品創建: ${menuItem.name} by ${req.user?.name}`)

  res.status(201).json({
    success: true,
    message: '菜品創建成功',
    data: { menuItem },
  })
})

// 更新菜品
const updateMenuItem = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params
  const {
    name,
    description,
    price,
    cost,
    categoryId,
    stock,
    lowStockAlert,
    prepTime,
    image,
    isActive,
    isAvailable,
  } = req.body
  const userStoreId = req.user?.storeId

  const existingMenuItem = await prisma.menuItem.findUnique({
    where: { id },
  })

  if (!existingMenuItem) {
    throw new NotFoundError('菜品')
  }

  // 檢查門店權限
  if (userStoreId && existingMenuItem.storeId !== userStoreId) {
    throw new ForbiddenError('無權限操作此菜品')
  }

  // 如果更新分類，驗證新分類
  if (categoryId && categoryId !== existingMenuItem.categoryId) {
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    })

    if (!category) {
      throw new NotFoundError('菜品分類')
    }

    if (category.storeId !== existingMenuItem.storeId) {
      throw new ForbiddenError('無權限使用此分類')
    }
  }

  // 如果更新名稱，檢查重複
  if (name && name !== existingMenuItem.name) {
    const duplicateMenuItem = await prisma.menuItem.findFirst({
      where: {
        name,
        storeId: existingMenuItem.storeId,
        id: { not: id },
      },
    })

    if (duplicateMenuItem) {
      throw new ValidationError('菜品名稱已存在')
    }
  }

  const updatedMenuItem = await prisma.menuItem.update({
    where: { id },
    data: {
      name,
      description,
      price,
      cost,
      categoryId,
      stock,
      lowStockAlert,
      prepTime,
      image,
      isActive,
      isAvailable,
    },
    include: {
      category: {
        select: {
          id: true,
          name: true,
          description: true,
          sortOrder: true,
        },
      },
    },
  })

  logger.info(`菜品更新: ${updatedMenuItem.name} by ${req.user?.name}`)

  res.json({
    success: true,
    message: '菜品更新成功',
    data: { menuItem: updatedMenuItem },
  })
})

// 刪除菜品
const deleteMenuItem = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params
  const userStoreId = req.user?.storeId

  const existingMenuItem = await prisma.menuItem.findUnique({
    where: { id },
  })

  if (!existingMenuItem) {
    throw new NotFoundError('菜品')
  }

  // 檢查門店權限
  if (userStoreId && existingMenuItem.storeId !== userStoreId) {
    throw new ForbiddenError('無權限操作此菜品')
  }

  // 檢查是否有關聯的訂單項目
  const orderItemCount = await prisma.orderItem.count({
    where: { menuItemId: id },
  })

  if (orderItemCount > 0) {
    // 如果有關聯訂單，只是標記為不活躍而不是物理刪除
    const updatedMenuItem = await prisma.menuItem.update({
      where: { id },
      data: {
        isActive: false,
        isAvailable: false,
      },
    })

    logger.info(`菜品停用: ${updatedMenuItem.name} by ${req.user?.name}`)

    res.json({
      success: true,
      message: '菜品已停用（因為存在關聯訂單）',
      data: { menuItem: updatedMenuItem },
    })
  } else {
    // 沒有關聯訂單，可以物理刪除
    await prisma.menuItem.delete({
      where: { id },
    })

    logger.info(`菜品刪除: ${existingMenuItem.name} by ${req.user?.name}`)

    res.json({
      success: true,
      message: '菜品刪除成功',
    })
  }
})

// 更新庫存
const updateStock = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params
  const { stock, operation = 'set' } = req.body // operation: 'set', 'add', 'subtract'
  const userStoreId = req.user?.storeId

  const existingMenuItem = await prisma.menuItem.findUnique({
    where: { id },
  })

  if (!existingMenuItem) {
    throw new NotFoundError('菜品')
  }

  // 檢查門店權限
  if (userStoreId && existingMenuItem.storeId !== userStoreId) {
    throw new ForbiddenError('無權限操作此菜品')
  }

  let newStock: number
  const currentStock = existingMenuItem.stock || 0

  switch (operation) {
    case 'add':
      newStock = currentStock + stock
      break
    case 'subtract':
      newStock = Math.max(0, currentStock - stock)
      break
    case 'set':
    default:
      newStock = stock
      break
  }

  const updatedMenuItem = await prisma.menuItem.update({
    where: { id },
    data: { stock: newStock },
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  // 記錄庫存變動日誌
  // TODO: 實現庫存日誌記錄

  logger.info(`庫存更新: ${updatedMenuItem.name} ${currentStock} -> ${newStock} by ${req.user?.name}`)

  res.json({
    success: true,
    message: '庫存更新成功',
    data: { menuItem: updatedMenuItem },
  })
})

export const menuItemController = {
  getMenuItems,
  getMenuItem,
  createMenuItem,
  updateMenuItem,
  deleteMenuItem,
  updateStock,
}
