import { Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { prisma } from '@/config/database'
import { catchAsync } from '@/middleware/errorHandler'
import { UnauthorizedError, ConflictError, NotFoundError, ValidationError } from '@/middleware/errorHandler'
import { logger } from '@/utils/logger'

// JWT 工具函數
const generateToken = (userId: string, email: string, role: string) => {
  return jwt.sign(
    { userId, email, role },
    process.env.JWT_SECRET!,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  )
}

const generateRefreshToken = (userId: string) => {
  return jwt.sign(
    { userId },
    process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET!,
    { expiresIn: '30d' }
  )
}

// 登入
const login = catchAsync(async (req: Request, res: Response) => {
  const { email, password } = req.body

  // 查找用戶
  const user = await prisma.user.findUnique({
    where: { email },
    include: { store: true },
  })

  if (!user) {
    throw new UnauthorizedError('電子郵件或密碼錯誤')
  }

  if (!user.isActive) {
    throw new UnauthorizedError('帳號已停用，請聯繫管理員')
  }

  // 驗證密碼
  const isPasswordValid = await bcrypt.compare(password, user.password)
  if (!isPasswordValid) {
    throw new UnauthorizedError('電子郵件或密碼錯誤')
  }

  // 生成令牌
  const token = generateToken(user.id, user.email, user.role)
  const refreshToken = generateRefreshToken(user.id)

  // 記錄登入日誌
  logger.info(`用戶登入: ${user.email} (${user.role})`)

  res.json({
    success: true,
    message: '登入成功',
    data: {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        role: user.role,
        storeId: user.storeId,
        store: user.store ? {
          id: user.store.id,
          name: user.store.name,
        } : null,
      },
      token,
      refreshToken,
    },
  })
})

// 註冊
const register = catchAsync(async (req: Request, res: Response) => {
  const { email, username, password, name, role = 'CASHIER', storeId } = req.body

  // 檢查電子郵件是否已存在
  const existingUserByEmail = await prisma.user.findUnique({
    where: { email },
  })

  if (existingUserByEmail) {
    throw new ConflictError('電子郵件已被使用')
  }

  // 檢查用戶名是否已存在
  const existingUserByUsername = await prisma.user.findUnique({
    where: { username },
  })

  if (existingUserByUsername) {
    throw new ConflictError('用戶名已被使用')
  }

  // 如果指定了門店，檢查門店是否存在
  if (storeId) {
    const store = await prisma.store.findUnique({
      where: { id: storeId },
    })

    if (!store) {
      throw new NotFoundError('指定的門店')
    }
  }

  // 加密密碼
  const hashedPassword = await bcrypt.hash(password, 12)

  // 創建用戶
  const user = await prisma.user.create({
    data: {
      email,
      username,
      password: hashedPassword,
      name,
      role,
      storeId,
    },
    include: { store: true },
  })

  // 生成令牌
  const token = generateToken(user.id, user.email, user.role)
  const refreshToken = generateRefreshToken(user.id)

  // 記錄註冊日誌
  logger.info(`新用戶註冊: ${user.email} (${user.role})`)

  res.status(201).json({
    success: true,
    message: '註冊成功',
    data: {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        role: user.role,
        storeId: user.storeId,
        store: user.store ? {
          id: user.store.id,
          name: user.store.name,
        } : null,
      },
      token,
      refreshToken,
    },
  })
})

// 登出
const logout = catchAsync(async (req: Request, res: Response) => {
  // 在實際應用中，可以將令牌加入黑名單
  // 這裡簡單返回成功消息
  logger.info(`用戶登出: ${req.user?.email}`)

  res.json({
    success: true,
    message: '登出成功',
  })
})

// 刷新令牌
const refreshToken = catchAsync(async (req: Request, res: Response) => {
  const { refreshToken } = req.body

  if (!refreshToken) {
    throw new UnauthorizedError('刷新令牌缺失')
  }

  try {
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET!
    ) as any

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
    })

    if (!user || !user.isActive) {
      throw new UnauthorizedError('用戶不存在或已停用')
    }

    const newToken = generateToken(user.id, user.email, user.role)
    const newRefreshToken = generateRefreshToken(user.id)

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        token: newToken,
        refreshToken: newRefreshToken,
      },
    })
  } catch (error) {
    throw new UnauthorizedError('無效的刷新令牌')
  }
})

// 獲取當前用戶信息
const getCurrentUser = catchAsync(async (req: Request, res: Response) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    include: { store: true },
    select: {
      id: true,
      email: true,
      username: true,
      name: true,
      role: true,
      isActive: true,
      storeId: true,
      store: {
        select: {
          id: true,
          name: true,
          address: true,
          phone: true,
        },
      },
      createdAt: true,
      updatedAt: true,
    },
  })

  if (!user) {
    throw new NotFoundError('用戶')
  }

  res.json({
    success: true,
    data: { user },
  })
})

// 修改密碼
const changePassword = catchAsync(async (req: Request, res: Response) => {
  const { currentPassword, newPassword } = req.body
  const userId = req.user!.id

  // 獲取用戶當前密碼
  const user = await prisma.user.findUnique({
    where: { id: userId },
  })

  if (!user) {
    throw new NotFoundError('用戶')
  }

  // 驗證當前密碼
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
  if (!isCurrentPasswordValid) {
    throw new ValidationError('當前密碼錯誤')
  }

  // 檢查新密碼是否與當前密碼相同
  const isSamePassword = await bcrypt.compare(newPassword, user.password)
  if (isSamePassword) {
    throw new ValidationError('新密碼不能與當前密碼相同')
  }

  // 加密新密碼
  const hashedNewPassword = await bcrypt.hash(newPassword, 12)

  // 更新密碼
  await prisma.user.update({
    where: { id: userId },
    data: { password: hashedNewPassword },
  })

  logger.info(`用戶修改密碼: ${user.email}`)

  res.json({
    success: true,
    message: '密碼修改成功',
  })
})

// 忘記密碼
const forgotPassword = catchAsync(async (req: Request, res: Response) => {
  const { email } = req.body

  const user = await prisma.user.findUnique({
    where: { email },
  })

  if (!user) {
    // 為了安全，即使用戶不存在也返回成功消息
    res.json({
      success: true,
      message: '如果該電子郵件地址存在，重置密碼鏈接已發送',
    })
    return
  }

  // 生成重置令牌
  const resetToken = jwt.sign(
    { userId: user.id, email: user.email },
    process.env.JWT_SECRET!,
    { expiresIn: '1h' }
  )

  // 在實際應用中，這裡應該發送郵件
  // 現在只是記錄日誌
  logger.info(`密碼重置請求: ${email}, 令牌: ${resetToken}`)

  res.json({
    success: true,
    message: '如果該電子郵件地址存在，重置密碼鏈接已發送',
    // 開發環境下返回令牌
    ...(process.env.NODE_ENV === 'development' && { resetToken }),
  })
})

// 重置密碼
const resetPassword = catchAsync(async (req: Request, res: Response) => {
  const { token, password } = req.body

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
    })

    if (!user) {
      throw new UnauthorizedError('無效的重置令牌')
    }

    // 加密新密碼
    const hashedPassword = await bcrypt.hash(password, 12)

    // 更新密碼
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword },
    })

    logger.info(`用戶重置密碼: ${user.email}`)

    res.json({
      success: true,
      message: '密碼重置成功',
    })
  } catch (error) {
    throw new UnauthorizedError('無效或已過期的重置令牌')
  }
})

export const authController = {
  login,
  register,
  logout,
  refreshToken,
  getCurrentUser,
  changePassword,
  forgotPassword,
  resetPassword,
}
