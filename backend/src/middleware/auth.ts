import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { prisma } from '@/config/database'
import { logger } from '@/utils/logger'

// 擴展 Request 接口
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string
        email: string
        username: string
        name: string
        role: string
        storeId?: string
      }
    }
  }
}

// JWT 載荷接口
interface JWTPayload {
  userId: string
  email: string
  role: string
  iat: number
  exp: number
}

// 認證中間件
export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '認證令牌缺失或格式錯誤',
      })
    }
    
    const token = authHeader.substring(7) // 移除 "Bearer " 前綴
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '認證令牌缺失',
      })
    }
    
    // 驗證 JWT
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload
    
    // 從資料庫獲取用戶信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        isActive: true,
        storeId: true,
      },
    })
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用戶不存在',
      })
    }
    
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: '用戶帳號已停用',
      })
    }
    
    // 將用戶信息添加到請求對象
    req.user = {
      id: user.id,
      email: user.email,
      username: user.username,
      name: user.name,
      role: user.role,
      storeId: user.storeId || undefined,
    }
    
    next()
  } catch (error) {
    logger.error('認證中間件錯誤:', error)
    
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        success: false,
        message: '無效的認證令牌',
      })
    }
    
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        success: false,
        message: '認證令牌已過期',
      })
    }
    
    return res.status(500).json({
      success: false,
      message: '認證過程中發生錯誤',
    })
  }
}

// 角色權限檢查中間件
export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未認證的用戶',
      })
    }
    
    const allowedRoles = Array.isArray(roles) ? roles : [roles]
    
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '權限不足',
        required: allowedRoles,
        current: req.user.role,
      })
    }
    
    next()
  }
}

// 門店權限檢查中間件
export const requireStore = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user?.storeId) {
    return res.status(403).json({
      success: false,
      message: '用戶未分配到任何門店',
    })
  }
  
  next()
}

// 管理員權限檢查
export const requireAdmin = requireRole(['ADMIN'])

// 管理員或店長權限檢查
export const requireManager = requireRole(['ADMIN', 'MANAGER'])

// 收銀員權限檢查
export const requireCashier = requireRole(['ADMIN', 'MANAGER', 'CASHIER'])

// 廚房權限檢查
export const requireKitchen = requireRole(['ADMIN', 'MANAGER', 'KITCHEN'])

// 可選認證中間件（不強制要求認證）
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next()
    }
    
    const token = authHeader.substring(7)
    
    if (!token) {
      return next()
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload
    
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        isActive: true,
        storeId: true,
      },
    })
    
    if (user && user.isActive) {
      req.user = {
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        role: user.role,
        storeId: user.storeId || undefined,
      }
    }
    
    next()
  } catch (error) {
    // 可選認證失敗時不返回錯誤，繼續處理請求
    next()
  }
}
