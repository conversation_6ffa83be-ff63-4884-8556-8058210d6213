import { Request, Response, NextFunction } from 'express'
import { Prisma } from '@prisma/client'
import { logger } from '@/utils/logger'

// 自定義錯誤類
export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational

    Error.captureStackTrace(this, this.constructor)
  }
}

// 驗證錯誤類
export class ValidationError extends AppError {
  public errors: any[]

  constructor(message: string, errors: any[] = []) {
    super(message, 400)
    this.errors = errors
  }
}

// 未找到錯誤類
export class NotFoundError extends AppError {
  constructor(resource: string = '資源') {
    super(`${resource}未找到`, 404)
  }
}

// 權限錯誤類
export class ForbiddenError extends AppError {
  constructor(message: string = '權限不足') {
    super(message, 403)
  }
}

// 未授權錯誤類
export class UnauthorizedError extends AppError {
  constructor(message: string = '未授權訪問') {
    super(message, 401)
  }
}

// 衝突錯誤類
export class ConflictError extends AppError {
  constructor(message: string = '資源衝突') {
    super(message, 409)
  }
}

// Prisma 錯誤處理
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): AppError => {
  switch (error.code) {
    case 'P2002':
      // 唯一約束違反
      const field = error.meta?.target as string[]
      return new ConflictError(`${field?.join(', ') || '欄位'} 已存在`)
    
    case 'P2025':
      // 記錄未找到
      return new NotFoundError('記錄')
    
    case 'P2003':
      // 外鍵約束違反
      return new AppError('關聯資料不存在', 400)
    
    case 'P2014':
      // 關聯記錄衝突
      return new ConflictError('無法刪除，存在關聯資料')
    
    case 'P2021':
      // 表不存在
      return new AppError('資料表不存在', 500)
    
    case 'P2022':
      // 欄位不存在
      return new AppError('資料欄位不存在', 500)
    
    default:
      logger.error('未處理的 Prisma 錯誤:', error)
      return new AppError('資料庫操作失敗', 500)
  }
}

// 開發環境錯誤響應
const sendErrorDev = (err: AppError, res: Response) => {
  res.status(err.statusCode).json({
    success: false,
    error: {
      message: err.message,
      stack: err.stack,
      statusCode: err.statusCode,
      isOperational: err.isOperational,
    },
  })
}

// 生產環境錯誤響應
const sendErrorProd = (err: AppError, res: Response) => {
  // 操作錯誤：發送錯誤消息給客戶端
  if (err.isOperational) {
    const response: any = {
      success: false,
      message: err.message,
    }

    // 如果是驗證錯誤，包含詳細錯誤信息
    if (err instanceof ValidationError) {
      response.errors = err.errors
    }

    res.status(err.statusCode).json(response)
  } else {
    // 程式錯誤：不洩露錯誤詳情
    logger.error('程式錯誤:', err)
    
    res.status(500).json({
      success: false,
      message: '服務器內部錯誤',
    })
  }
}

// 全局錯誤處理中間件
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let error = err as AppError

  // 記錄錯誤
  logger.error('錯誤處理中間件捕獲錯誤:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
  })

  // 處理 Prisma 錯誤
  if (err instanceof Prisma.PrismaClientKnownRequestError) {
    error = handlePrismaError(err)
  }
  
  // 處理 Prisma 驗證錯誤
  else if (err instanceof Prisma.PrismaClientValidationError) {
    error = new ValidationError('資料驗證失敗')
  }
  
  // 處理 JSON 解析錯誤
  else if (err instanceof SyntaxError && 'body' in err) {
    error = new ValidationError('無效的 JSON 格式')
  }
  
  // 處理 JWT 錯誤
  else if (err.name === 'JsonWebTokenError') {
    error = new UnauthorizedError('無效的認證令牌')
  }
  
  else if (err.name === 'TokenExpiredError') {
    error = new UnauthorizedError('認證令牌已過期')
  }
  
  // 處理 Multer 錯誤
  else if (err.name === 'MulterError') {
    error = new ValidationError('文件上傳失敗')
  }
  
  // 如果不是 AppError 實例，創建一個通用錯誤
  else if (!(err instanceof AppError)) {
    error = new AppError(err.message || '服務器內部錯誤', 500, false)
  }

  // 根據環境發送錯誤響應
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(error, res)
  } else {
    sendErrorProd(error, res)
  }
}

// 異步錯誤捕獲包裝器
export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}
